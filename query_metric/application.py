# --*-- conding:utf-8 --*--
# @Time : 2025/2/12
# <AUTHOR> <PERSON>



from urllib.parse import quote_plus

from fastapi import FastAPI, Depends
from .config import CFG


class Application(FastAPI):

    def init_mongodb(self,
                     host: str = None,
                     port: int = None,
                     username: str = '',
                     password: str = '',
                     db: str = None,
                     auth_source: str = None,
                     alias: str = 'default',
                     ):
        """
        Initialize MongoDB connection.
        :param host:
        :param port:
        :param username:
        :param password:
        :param db:
        :param auth_source:
        :param alias:
        :return:
        """

        db_dbname = db if db else CFG.db_name
        db_host = host if host else CFG.db_host
        db_port = port if port else CFG.db_port
        db_username = username if username else CFG.db_username
        db_password = password if password else CFG.db_password
        db_auth_source = auth_source if auth_source else CFG.MONGODB_AUTH_SOURCE

        db_uri = f"mongodb://{db_host}:{db_port}/{db_dbname}"
        if db_username and db_password:
            encoded_username = quote_plus(db_username)
            encoded_password = quote_plus(db_password)
            db_uri = f"mongodb://{encoded_username}:{encoded_password}@{db_host}:{db_port}/{db_dbname}"

        return mongoengine_connect(
            db=db_dbname,
            host=db_uri,
            username=db_username,
            password=db_password,
            alias=alias,
            authentication_source=db_auth_source,
            connect=False
        )


app = Application(name='query_metric')
