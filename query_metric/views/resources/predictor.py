# --*-- conding:utf-8 --*--
# @Time : 2024/12/24
# <AUTHOR> <PERSON>
import time
import traceback

from fastapi import Request

from query_metric.base.fastapi_util import BaseHTTPView
from query_metric.router import index as router
from query_metric.common.log_util import logger
from query_metric.model import model

__all__ = (
    'ModelPredictView',
)


class ModelPredictView(BaseHTTPView):

    async def post(self, request: Request):
        """

        :param request:
        :return:
        """
        start_time = time.time()
        # 提取入参
        req_data = await self.get_json(request)

        try:
            outputs = model.predict(req_data)
        except Exception as e:
            logger.error(f'Model predict error: {traceback.format_exc()}')
            logger.error(f'inputs: {req_data}')
            return self.Jsonify.fail(message=str(e.__str__()))

        logger.info(f'All used time: {(time.time() - start_time) * 1000}ms')
        return self.Jsonify.success(
            data=outputs
        )


router.add_api_route(path="/predict", endpoint=ModelPredictView().post, methods=["POST"], )
