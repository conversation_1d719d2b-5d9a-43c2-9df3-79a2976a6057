# --*-- conding:utf-8 --*--
# @Time : 2024/12/24
# <AUTHOR> <PERSON>


import os
import time
import traceback
import signal

from fastapi import Request
from fastapi import FastAPI, Response
from prometheus_client import generate_latest, CONTENT_TYPE_LATEST

from query_metric.base.fastapi_util import BaseHTTPView
from query_metric.common.log_util import logger
from query_metric.common.eureka_utils import EurekaManager
from query_metric.router import index as router


__all__ = (
    'ManageEurekaUpView',
    'ManageEurekaDownView',
    'ManageEurekaHealthView',
)


class ManageEurekaUpView(BaseHTTPView):

    async def post(self, request: Request):
        """

        :param request:
        :return:
        """

        try:
            logger.info("Starting registration with <PERSON>.")
            logger.info(f"{os.environ.get('CONFIG_ENCRYPTOR_PASSWORD')}")
            eureka_manager = EurekaManager()
            await eureka_manager.up()
            logger.info("Successfully completed registration with <PERSON>.")
        except Exception as e:
            logger.error(f'Failed to register with <PERSON>: \n{traceback.format_exc()}')
            raise e

        # try:
        #     loader = KnowledgeBaseLoader(vector_service_url='http://127.0.0.1:10240',
        #                                  knowledge_base_path=CFG.knowledge_base)
        #
        #     # Check vector service availability
        #     if not loader.check_vector_service():
        #         logger.error("Vector service is not available. Please start it first:")
        #         raise Exception("Vector service is not available. Please start it first:")
        #
        #     # Load documents
        #     success = loader.load_documents_to_vector_db()
        #     if success:
        #         logger.info("Knowledge base loading completed successfully")
        #
        #         # Show final stats
        #         stats = loader.get_service_stats()
        #         logger.info(f"Vector database now contains {stats.get('total_documents', 0)} documents")
        #     else:
        #         logger.error("Knowledge base loading failed")
        #         raise Exception("Knowledge base loading failed")
        # except Exception as e:
        #     logger.error(f'Initialization of weight_data and corpus_data failed.: \n{traceback.format_exc()}')
        #     raise e

        return self.Jsonify.success()


class ManageEurekaDownView(BaseHTTPView):

    async def post(self, request: Request):
        """

        :param request:
        :return:
        """

        try:
            eureka_manager = EurekaManager()
            await eureka_manager.down()
            os.kill(os.getpid(), signal.SIGTERM)
            time.sleep(10)
            os.kill(os.getpid(), signal.SIGKILL)
            return self.Jsonify.success()
        except Exception as e:
            logger.error(f'Failed to execute eureka down: {traceback.format_exc()}')
            raise e


class ManageEurekaHealthView(BaseHTTPView):
    async def get(self, request: Request):
        """

        :param request:
        :return:
        """

        logger.info('Service is healthy...')
        return self.Jsonify.success()


class ManagePrometheusMetricView(BaseHTTPView):

    async def get(self, request: Request):
        """

        :param request:
        :return:
        """
        metrics = generate_latest()
        return Response(content=metrics, media_type=CONTENT_TYPE_LATEST)


router.add_api_route(path="/actuator/up", endpoint=ManageEurekaUpView().post, methods=["POST"], )
router.add_api_route(path="/actuator/down", endpoint=ManageEurekaDownView().post, methods=["POST"], )
router.add_api_route(path="/actuator/health", endpoint=ManageEurekaHealthView().get, methods=["GET"], )
router.add_api_route(path="/actuator/prometheus", endpoint=ManagePrometheusMetricView().get, methods=["GET"], )
