# --*-- conding:utf-8 --*--
# @Time : 2024/11/21
# <AUTHOR> <PERSON>

import socket
import traceback

from py_eureka_client import eureka_client
from werkzeug.serving import get_interface_ip

from ..common.log_util import logger
from ..config import CFG
from ..utils.enc import des_descrypt

__all__ = (
    'EurekaManager',
)


class EurekaManager:

    def __init__(self):

        self.eureka_server = CFG.eureka_server
        self.eureka_username = CFG.eureka_username
        # self.eureka_password = des_descrypt(CFG.eureka_password, CFG.secret_key)
        self.eureka_password = CFG.eureka_password
        self.server_name = CFG.server_name
        self.server_ip = get_interface_ip(socket.AF_INET)

    async def init(self):
        try:
            await eureka_client.init_async(
                eureka_server=self.eureka_server,
                eureka_basic_auth_user=self.eureka_username,
                eureka_basic_auth_password=self.eureka_password,
                app_name=self.server_name,
                instance_port=10240,
                status_page_url="/actuator/info",
                instance_host=self.server_ip,
                instance_ip=self.server_ip
            )
        except Exception as e:
            logger.error(f"Failed to initialize Eureka client: {e}")
            raise e

    async def up(self):
        if eureka_client.get_client() is None:
            logger.info("eureka_client.get_client() is None...")
            await self.init()

    async def down(self):
        try:
            logger.info("Stopping Eureka client...")
            await eureka_client.stop_async()
            logger.info("Eureka client stopped successfully.")
        except Exception as e:
            logger.error(f"Failed to stop Eureka client: {traceback.format_exc()}")
            raise

# eureka_manager = EurekaManager()
