# --*-- conding:utf-8 --*--
# @Time : 2024/12/4
# <AUTHOR> <PERSON>


import os
import inspect
from loguru import logger as base_logger


class Logger:
    register = dict()
    is_init = True

    def __new__(cls, log_name='common', log_path=None):

        if not log_path or not isinstance(log_path, str):
            log_path = './logs/'
        if log_path not in cls.register:
            logger = super().__new__(cls)
            logger.log_path = log_path
            logger.log_name = log_name
            logger.logger = base_logger
            cls.register[log_name] = logger
        return cls.register[log_name]

    def reload(self, log_path):
        self.log_path = log_path
        return self

    def _prepare_log_directory(self):
        """
        准备日志目录
        :return:
        """
        if not os.path.exists(self.log_path):
            os.makedirs(self.log_path)

        # common_log_path = os.path.join(self.log_path, "common.log")
        # if not os.path.exists(common_log_path):
        #     with open(common_log_path, "w") as f:
        #         f.write("")

    def _setup_log_rotation(self):
        """
        日志切分: 每天12点将当天的日志备份成common.{time:YYYY-MM-DD}.log 格式
        :return:
        """

        log_path = os.path.join(self.log_path, f"{self.log_name}.log")
        self.logger.add(
            log_path,
            rotation="00:00",
            retention="30 days",
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | {level} | <level>{message}</level>",
            enqueue=True,
        )

    def info(self, msg: str):
        self.logger.info(f"{self._get_caller()} {msg}")

    def warning(self, msg: str):
        self.logger.warning(f"{self._get_caller()} {msg}")

    def error(self, msg: str):
        self.logger.error(f"{self._get_caller()}\n {msg}")

    def _get_caller(self):
        """

        :return:
        """
        if self.is_init:
            self._prepare_log_directory()
            self._setup_log_rotation()
            self.is_init = False

        stack = inspect.stack()
        if len(stack) < 3:
            return "UNKNOWN | line: ? | func: ?"

        frame = stack[2]
        filename = frame.filename
        lineno = frame.lineno
        func_name = frame.function

        file_parts = filename.split('/')
        if len(file_parts) > 1:
            file_display = f"{file_parts[-2]}.{file_parts[-1]}"
        else:
            file_display = os.path.basename(filename)

        return f"{file_display} | line: {lineno} | func: {func_name} |"

    # def _get_caller(self):
    #     """
    #
    #     :return:
    #     """
    #     if self.is_init:
    #         self._prepare_log_directory()
    #         self._setup_log_rotation()
    #         self.is_init = False
    #     stack = inspect.stack()
    #     filename = stack[2][1].split('/')[-2].split('.')[0] + '.' + stack[2][1].split('/')[-1]
    #     lineno = stack[2][2]
    #     func_name = stack[2][3]
    #     return f"{filename} | line：{lineno} ｜ func:{func_name} |"


logger = Logger()

# if __name__ == '__main__':
#     logger = Logger(log_path="./logs1/")
#     logger.info("This is the latest log entry.")
#     logger.warning("Another log for testing rotation.")
