# --*-- conding:utf-8 --*--
# @Time : 2024/11/20
# <AUTHOR> <PERSON>


from pyapollo.apollo_client import ApolloClient
from ..utils import enc


class ApolloManager(object):
    def __init__(self, hosts, app_id, cache_file_path):
        """

        :param hosts:
        :param app_id:
        :param cache_file_path:
        """
        self.client = ApolloClient(
            app_id=app_id,
            config_server_url=hosts,
            timeout=10,
            cache_file_path=cache_file_path
        )
        self.client.start(True)

    def get_value(self, key, default_val=None):
        if default_val is None:
            return enc.decrypt_enc(self.client.get_value(key))
        else:
            return enc.decrypt_enc(self.client.get_value(key, default_val))
