# --*-- conding:utf-8 --*--
# @Time : 2024/12/23
# <AUTHOR> <PERSON>




import os
import six
import yaml

from .common.apollo_utils import ApolloManager

__all__ = (
    "CFG",
)


class Config:
    def __init__(self, conf_path: str = ''):
        """

        :param conf_path:
        """

        if not conf_path:
            conf_path = './etc/server.default.conf'

        self.conf_path = conf_path
        self.conf_data = {}
        # self.reload()
        self.init()

    def init(self):
        self.conf_data = self.get_file_conf_data()
        return self

    def reload(self, r_conf_path: str = '', r_conf_data: dict = None):
        """

        :param r_conf_path:
        :param r_conf_data:
        :return:
        """
        file_conf_data = self.conf_data
        if r_conf_path:
            self.conf_path = r_conf_path
            file_conf_data = self.get_file_conf_data()

        self.conf_data = self.get_apollo_conf_data(file_conf_data)
        if r_conf_data:
            self.update(r_conf_data)
        return self

    def get_file_conf_data(self) -> dict:
        """
        读取配置文件中的配置
        :return:
        """
        if not os.path.exists(self.conf_path):
            raise

        if not os.access(self.conf_path, os.R_OK):
            raise

        file_conf_data = dict()
        with open(self.conf_path, 'r', encoding='utf-8') as f:
            file_data = yaml.safe_load(f.read()) or {}
            for k, v in file_data.items():
                if isinstance(v, six.string_types):
                    v = v.strip()
                file_conf_data[k.lower()] = v
        return file_conf_data

    def get_apollo_conf_data(self, file_conf_data: dict) -> dict:
        """
        获取apollo中的配置, 根据配置文件中的key全部遍历一遍
        :param file_conf_data: 配置文件中的配置
        :return:
        """

        is_apollo = file_conf_data.get('is_apollo', True)
        if not is_apollo:
            return file_conf_data

        apollo_hosts = os.environ.get("APOLLO_HOSTS", "http://***********:8080/")
        apollo_app_id = file_conf_data.get('appollo_id', '') or file_conf_data['server_name']
        cache_file_path = os.path.join('/tmp', 'config')
        apollo_cli = ApolloManager(hosts=apollo_hosts, app_id=apollo_app_id.upper(), cache_file_path=cache_file_path)

        apollo_conf_data = dict()
        for k, v in file_conf_data.items():
            v = apollo_cli.get_value(k, v)
            apollo_conf_data[k] = v
        return apollo_conf_data

    def update(self, cfg_data: dict):
        """
        :param cfg_data:
        :return:
        """
        for k, v in cfg_data.items():
            self.conf_data[k.lower()] = v
        return self

    def __getattr__(self, k: str):
        """
        :param k:
        :return:
        """
        return self.conf_data.get(k.lower())


CFG = Config()
