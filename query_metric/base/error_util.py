# --*-- conding:utf-8 --*--
# @Time : 2024/12/23
# <AUTHOR> <PERSON>

import json

from fastapi import HTTPException

__all__ = (
    'DataCovertException',
    'SafeSaveException',
    'APIHTTPAuthError',
)


class DataCovertException(Exception):
    pass


class SafeSaveException(Exception):
    pass


class APIHTTPAuthError(HTTPException):

    def __init__(self, status_code=401, log_message=None, *args, **kwargs):
        """
        :param status_code:
        :param log_message:
        :param return_data:
        :param return_code:
        :param args:
        :param kwargs:
        """
        super().__init__(status_code=status_code, message=log_message)
        self.status_code = status_code
        self.log_message = log_message if log_message else '认证失败'

    def __str__(self):
        """
        :return:
        """
        err = {
            'code': str(self.status_code),
            'err_level': 0,
            'message': self.log_message,
            'data': {},
            'extra': {}
        }
        return json.dumps(err)
