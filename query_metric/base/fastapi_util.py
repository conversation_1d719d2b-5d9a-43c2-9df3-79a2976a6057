# --*-- conding:utf-8 --*--
# @Time : 2024/12/24
# <AUTHOR> <PERSON>


from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.responses import JSONResponse
from typing import Any, Dict, Optional


# 默认消息
DEFAULT_CREATE_SUCCESS_MESSAGE = '创建成功'
DEFAULT_CREATE_FAIL_MESSAGE = '创建失败'
DEFAULT_SUCCESS_MESSAGE = '操作成功.'
DEFAULT_FAIL_MESSAGE = '操作失败.'
DEFAULT_DATA_ERR_MESSAGE = '数据错误.'
DEFAULT_SAVE_ERR_MESSAGE = '保存失败.'


class Jsonify:
    """
    统一响应格式工具类
    """

    @staticmethod
    def output(
        code: int = 200,
        success=False,
        message: str = '',
        data: Optional[Dict[str, Any]] = None,
        extra: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> JSONResponse:
        """
        返回标准化响应
        :param code: 状态码
        :param success: 是否成功
        :param message: 消息
        :param data: 返回数据
        :param extra: 附加数据
        :param kwargs: 其他额外字段
        :return: JSONResponse
        """
        if data is None:
            data = {}
        if extra is None:
            extra = {}

        response_data = {
            "code": str(code),
            "success": success,
            "message": message,
            "data": data,
            "extra": extra,
            **kwargs
        }
        return JSONResponse(content=response_data)

    @staticmethod
    def success(code: int = 200, message: str = DEFAULT_SUCCESS_MESSAGE, **kwargs) -> JSONResponse:
        """
        操作成功
        :param code: 状态码
        :param message: 消息
        :param kwargs: 附加字段
        :return: JSONResponse
        """
        return Jsonify.output(code=code, success=True, message=message, **kwargs)

    @staticmethod
    def fail(code: int = 1000, message: str = DEFAULT_FAIL_MESSAGE, **kwargs) -> JSONResponse:
        """
        操作失败
        :param code: 状态码
        :param message: 消息
        :param kwargs: 附加字段
        :return: JSONResponse
        """
        return Jsonify.output(code=code, success=False, message=message, **kwargs)

    @staticmethod
    def data_error(code: int = 1000, message: str = DEFAULT_DATA_ERR_MESSAGE, **kwargs) -> JSONResponse:
        """
        数据错误
        :param code: 状态码
        :param message: 消息
        :param kwargs: 附加字段
        :return: JSONResponse
        """
        return Jsonify.output(code=code, message=message, **kwargs)

    @staticmethod
    def save_error(code: int = 1000, message: str = DEFAULT_SAVE_ERR_MESSAGE, **kwargs) -> JSONResponse:
        """
        保存失败
        :param code: 状态码
        :param message: 消息
        :param kwargs: 附加字段
        :return: JSONResponse
        """
        return Jsonify.output(code=code, message=message, **kwargs)


class BaseHTTPView:
    """
    基础视图类
    """

    def __init__(self):
        self.Jsonify = Jsonify()

    @staticmethod
    async def get_args(request: Request) -> Dict[str, Any]:
        """
        获取查询参数
        :param request: 请求对象
        :return: 参数字典
        """
        return dict(request.query_params)

    @staticmethod
    async def get_json(request: Request) -> Dict[str, Any]:
        """
        获取 JSON 请求体
        :param request: 请求对象
        :return: JSON 数据字典
        """
        try:
            return await request.json()
        except Exception as e:
            raise HTTPException(status_code=400, detail="Invalid JSON body") from e

