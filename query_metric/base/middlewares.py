# --*-- conding:utf-8 --*--
# @Time : 2024/12/26
# <AUTHOR> <PERSON>
import time
import traceback

from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import Response

from ..common.prometheus_utils import metric_cls_dict
from ..common.log_util import logger
from ..config import CFG


class CustomMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        try:
            response = await call_next(request)  # 继续请求
            # 执行请求后逻辑
            await process_metric(request, response, start_time=start_time)
        except Exception as e:
            logger.error(f'Internal Server Error: {traceback.format_exc()}')
            response = Response(
                content=f"Internal Server Error: {str(e)}",
                status_code=500
            )

        return response


async def process_metric(request, response, *args, **kwargs):
    """

    :param request:
    :param response:
    :param args:
    :param kwargs:
    :return:
    """
    endpoint = request.url
    method = request.method
    status = response.status_code
    start_time = kwargs.get('start_time')
    for metric_type, metric_cls in metric_cls_dict.items():
        metric_obj = metric_cls(server_name=CFG.server_name.upper())
        metric_obj.get_label(method=method, endpoint=endpoint, start_time=start_time, status=status)
