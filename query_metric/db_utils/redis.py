# --*-- conding:utf-8 --*--
# @Time : 2025/1/2
# <AUTHOR> <PERSON>

#
# import json
#
# from aioredis_cluster import create_redis_cluster
#
# from query_metric.config import CFG
#
# __all__ = (
#     'AsyncRedisClient',
# )
#
#
# class AsyncRedisClient:
#     def __init__(self, redis_url: str = None):
#         """
#         :param redis_url:
#         """
#         if not redis_url:
#             redis_url = CFG.redis_url
#         self.cluster_nodes = self._parse_nodes(redis_url)
#         self._client = None
#
#     def _parse_nodes(self, redis_url: str):
#         """
#
#         :param redis_url:
#         :return:
#         """
#         nodes = []
#         for node in redis_url.split(","):
#             host, port = node.split(":")
#             nodes.append((host.strip(), int(port)))
#         return nodes
#
#     async def connection(self):
#         """
#
#         :return:
#         """
#         if self._client is None:
#             self._client = await create_redis_cluster(self.cluster_nodes)
#         return self._client
#
#     async def set(self, key, data, expire: int = None):
#         """
#
#         :param key:
#         :param data:
#         :param expire:
#         :return:
#         """
#         client = await self.connection()
#         if isinstance(data, (dict, list, tuple)):
#             data = json.dumps(data)
#         await client.set(key, data)
#         if expire:
#             await client.expire(key, expire)
#
#     async def get(self, key):
#         """
#
#         :param key:
#         :return:
#         """
#         client = await self.connection()
#         data = await client.get(key)
#         return json.loads(data) if data else None
#
#
# if __name__ == '__main__':
#     async_redis = AsyncRedisClient()
