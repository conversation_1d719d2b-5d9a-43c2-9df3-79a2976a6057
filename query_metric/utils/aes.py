# --*-- conding:utf-8 --*--
# @Time : 2024/11/21
# <AUTHOR> <PERSON>

import base64

from Crypto.Cipher import AES

__all__ = (

    'decode_common_method',
    'encode_common_method',

)


class AesBase(object):
    """
    AES ECB 模式加密
    """

    def __init__(self, key):
        self.key = key
        self.mode = AES.MODE_ECB

    def padding(self, text):
        """
        对加密字符的处理
        """
        return text + (len(self.key) - len(text) % len(self.key)) * chr(len(self.key) - len(text) % len(self.key))

    def unpadding(self, text):
        """
        对解密字符的处理
        """
        return text[0:-ord(text[-1:])]

    def get_key_format(self, key):
        """
        对key的处理,key 的长度 16，24，32
        """
        key_len = len(key)
        if key_len <= 16:
            key += "0" * (16 - key_len)
        elif 16 < key_len <= 24:
            key += "0" * (24 - key_len)
        elif key_len <= 32:
            key += "0" * (32 - key_len)
        else:
            key = key[:32]
        return key

    # 加密函数
    def encrypt(self, text):
        cryptor = AES.new(self.key.encode("utf-8"), self.mode)  # ECB 模式
        ciphertext = cryptor.encrypt(bytes(self.padding(text), encoding="utf8"))
        encrypt_string = str(base64.b64encode(ciphertext)).lstrip("b")
        # 去除字符串两侧的引号 'sIYu5dAFfPrwZAHD' --> sIYu5dAFfPrwZAHD
        return encrypt_string

    # 解密函数
    def decrypt(self, text):
        decode = base64.b64decode(text)
        cryptor = AES.new(self.key.encode("utf8"), self.mode)  # ECB 模式
        plain_text = cryptor.decrypt(decode)
        decrypt_string = str(self.unpadding(plain_text)).lstrip("b")
        # 去除字符串两侧的引号 'sIYu5dAFfPrwZAHD' --> sIYu5dAFfPrwZAHD
        return decrypt_string


DEFAULT_AES_STICA_CLIP = "aLRp0oQ039DGPDm5"


def decode_common_method(encry_str: str) -> str:
    """
    解析数据库中对word字段进行解密
    """
    # 解密后去除特殊字符
    d_clip = DEFAULT_AES_STICA_CLIP
    aes = AesBase(d_clip)
    return aes.decrypt(encry_str)[1:-1]


def encode_common_method(origin_str: str) -> str:
    """
    对word 进行加密
    """
    d_clip = DEFAULT_AES_STICA_CLIP
    aes = AesBase(d_clip)
    return aes.encrypt(origin_str)[1:-1]


if __name__ == '__main__':
    content = 'P3ogkU7f3sPM'
    res = encode_common_method(content)
    print(res)
    res2 = decode_common_method(res)
    print(res2)
