# --*-- conding:utf-8 --*--
# @Time : 2024/12/23
# <AUTHOR> Chris

import platform
import click
import os
import setproctitle

from query_metric.setup import setup
from .application import app
from .config import CFG
from .common.log_util import logger
from .model import model

from .base.middlewares import CustomMiddleware

# import appsflyer.base.middleware


__version__ = '0.0.1'

banner = """
 -------------- MS@{model_name} V{model_version}
---- **** *---- 
--- * ***  * -- {platform}
-- * - ---- --- [config]
- ** ---------- .> apps    :     {server_name}
- ** ---------- .> info    :     {user}@{host}:{port}
- *** --- - --- .> python  :     {python}
-- ******* *--- .> config  :     {config}
--- *******---- .> log     :     {log_path}

"""


@click.option('-c', '--config', envvar='MS_CONFIG', type=click.STRING,
              help='指定配置文件,默认:当前项目下的ect目录')
@click.option('-h', '--host', type=click.STRING, help='指定监听的地址,默认:0.0.0.0')
@click.option('-p', '--port', type=click.INT, help='指定监听的端口,默认:5000-5003')
@click.option('--debug', type=click.BOOL, help='是否开启调试模式,默认:false')
@click.command(help="Run server in development mode")
def runserver(config: str = None, host: str = None, port: int = None,
              debug: bool = None):
    """
    启动服务，通过命令指定参数
    :param config: 配置文件地址
    :param host:
    :param port:
    :param debug:
    :return:
    """

    cfg_data = {}

    # 指定配置文件
    if config:
        config = os.path.abspath(config)
    if config and os.path.exists(config) and os.path.isfile(config):
        cfg_path = config
    else:
        cfg_path = './etc/server.default.conf'

    # 指定ip
    if host is not None:
        if host == '*':
            host = '0.0.0.0'
        cfg_data.update({'LISTEN_HOST': host})

    # 指定端口
    if port is not None:
        cfg_data.update({'LISTEN_PORT': int(port)})

    if debug is not None:
        cfg_data.update({'DEBUG': debug})

    # 将命令中指定的参数更新到配置文件中
    CFG.reload(r_conf_path=cfg_path, r_conf_data=cfg_data)
    logger.reload(log_path=CFG.log_path)

    if CFG.reload_model:
        logger.info(f'reload_model: {CFG.reload_model}')
        model.reload_model()

    setup(app)
    print(banner.format(model_name=CFG.server_name,
                        platform='platform.platform()',
                        host=CFG.LISTEN_HOST,
                        port=CFG.LISTEN_PORT,
                        server_name=CFG.server_name,
                        user=os.environ.get('USER', 'X'),
                        model_version=__version__,
                        python=platform.python_version(),
                        config=cfg_path,
                        log_path=CFG.log_path,
                        )
          )

    app.add_middleware(CustomMiddleware)
    setproctitle.setproctitle(CFG.server_name)


    import uvicorn
    uvicorn.run(app,
                host=CFG.LISTEN_HOST,
                port=CFG.LISTEN_PORT,
                )


@click.group()
def manage():
    pass


manage.add_command(runserver)

if __name__ == '__main__':
    runserver()
