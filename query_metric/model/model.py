# --*-- conding:utf-8 --*--
# @Time : 2025/2/13
# <AUTHOR> <PERSON>
import json
import os
import time
import joblib

from query_metric.common.log_util import logger
from query_metric.config import CFG
# from sentence_transformers import SentenceTransformer


class CustomModel:

    def __init__(self):
        self.model = None

    def reload_model(self):
        """

        :return:
        """
        # model_file = os.path.join(CFG.model_path, CFG.model_name)  # 构造完整路径
        # try:
        #     start_time = time.time()
        #     model = joblib.load(model_file)
        #     logger.info(f'load model successful, used time: {(time.time() - start_time) * 1000}ms')
        #     self.model = model
        # except Exception as e:
        #     logger.error(f'load model error: {model_file}')
        #     raise e
        self.instruction = "Represent this sentence for searching relevant passages: "
        # self.model = SentenceTransformer('gte-Qwen2-1.5B-instruct', trust_remote_code=True)

    def get_features(self):
        """

        :return:
        """
        pass

    def process(self, data):
        """

        :param data:
        :return:
        """
        start_time = time.time()
        logger.info(f'process successful, used time: {(time.time() - start_time) * 1000}ms')
        return data["query"]

    def predict(self, data):
        """

        :param data:
        :return:
        """
        start_time = time.time()
        query = self.process(data)
        outputs = self.model.encode([self.instruction + query], normalize_embeddings=True)
        logger.info(f'predict successful, used time: {(time.time() - start_time) * 1000}ms')
        return outputs[0].tolist()


model = CustomModel()