"""
技术指标计算类 - 使用 TA-Lib 计算各种技术指标

该模块提供了所有技术指标的计算方法，支持现货和期货市场的指标计算。
"""

import time
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime, timedelta
from query_metric.common.log_util import logger
from .config_helper import MetricConfig
from .data_fetcher import DataFetcher
from . import custom_indicators as talib


class TechnicalIndicators:
    """
    技术指标计算类
    
    该类封装了所有技术指标的计算方法，使用 TA-Lib 库进行计算。
    支持的指标包括：趋势指标、动量指标、波动率指标、成交量指标等。
    
    Attributes:
        fetcher (DataFetcher): 数据获取器实例
        config (MetricConfig): 配置对象
        
    Example:
        >>> indicators = TechnicalIndicators()
        >>> # 计算 RSI
        >>> rsi_result = indicators.calculate_rsi(
        ...     symbol="BTC-USDT",
        ...     interval="1hour",
        ...     period=14,
        ...     limit=100
        ... )
        >>> print(f"最新 RSI 值: {rsi_result['values'][-1]:.2f}")
        >>> 
        >>> # 计算 MACD
        >>> macd_result = indicators.calculate_macd(
        ...     symbol="ETH-USDT",
        ...     interval="4hour"
        ... )
    """
    
    def __init__(self):
        """初始化技术指标计算器"""
        self.fetcher = DataFetcher()
        self.config = MetricConfig
        
    def _get_kline_data(self, symbol: str, interval: str, market_type: str = "spot",
                       limit: Optional[int] = None, 
                       start_time: Optional[int] = None,
                       end_time: Optional[int] = None) -> pd.DataFrame:
        """
        获取并准备K线数据
        
        Args:
            symbol: 交易对或合约代码
            interval: 时间间隔
            market_type: 市场类型 "spot" 或 "futures"
            limit: 数据条数限制
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            包含 OHLCV 数据的 DataFrame
        """
        if market_type == "spot":
            klines = self.fetcher.get_spot_klines(symbol, interval, start_time, end_time, limit)
            df = self.fetcher.prepare_kline_data(klines, is_futures=False)
        elif market_type == "futures":
            klines = self.fetcher.get_futures_klines(symbol, interval, start_time, end_time, limit)
            df = self.fetcher.prepare_kline_data(klines, is_futures=True)
        else:
            raise ValueError(f"不支持的市场类型: {market_type}")
            
        return df
    
    def calculate_rsi(self, symbol: str, interval: str, 
                     period: int = 14, market_type: str = "spot",
                     limit: Optional[int] = None,
                     start_time: Optional[int] = None,
                     end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        计算相对强弱指数 (RSI)
        
        RSI 是一个动量振荡器，用于衡量价格变动的速度和幅度。
        RSI 值范围在 0-100 之间：
        - RSI > 70: 可能超买
        - RSI < 30: 可能超卖
        
        Args:
            symbol: 交易对（如 "BTC-USDT"）或合约代码（如 "BTCUSDTM"）
            interval: K线时间间隔（如 "1min", "1hour", "1day"）
            period: RSI 计算周期，默认 14
            market_type: 市场类型，"spot" 或 "futures"
            limit: 获取的K线数量限制
            start_time: 开始时间戳
            end_time: 结束时间戳
            
        Returns:
            计算结果字典：
            {
                "indicator": "RSI",
                "symbol": "BTC-USDT",
                "interval": "1hour",
                "period": 14,
                "values": [45.23, 48.56, ...],  # RSI 值数组
                "timestamps": [1234567890, ...],  # 对应的时间戳
                "latest_value": 52.34  # 最新的 RSI 值
            }
            
        Example:
            >>> # 计算 BTC 的 14 周期 RSI
            >>> result = indicators.calculate_rsi("BTC-USDT", "1hour")
            >>> if result["latest_value"] > 70:
            ...     print("RSI 显示超买信号")
            >>> 
            >>> # 使用自定义周期
            >>> result = indicators.calculate_rsi("ETH-USDT", "4hour", period=21)
        """
        try:
            # 获取足够的K线数据（需要额外的数据来计算第一个RSI值）
            min_required = self.config.get_min_klines_required("RSI") + period
            if limit and limit < min_required:
                limit = min_required
            elif not limit:
                limit = max(100, min_required)
                
            # 获取K线数据
            df = self._get_kline_data(symbol, interval, market_type, limit, start_time, end_time)
            
            if len(df) < period + 1:
                raise ValueError(f"数据不足：需要至少 {period + 1} 条K线数据，实际只有 {len(df)} 条")
            
            # 计算 RSI
            close_prices = df['close'].values
            rsi_values = talib.RSI(close_prices, timeperiod=period)
            
            # 移除 NaN 值
            valid_mask = ~np.isnan(rsi_values)
            rsi_values = rsi_values[valid_mask]
            timestamps = df.index[valid_mask].astype(np.int64) // 10**9  # 转换为秒时间戳
            
            # 获取最新值
            latest_rsi = rsi_values[-1] if len(rsi_values) > 0 else None
            
            result = {
                "period": period,
                "values": rsi_values.tolist(),
                "timestamps": timestamps.tolist(),
                "latest_value": float(latest_rsi) if latest_rsi else None
            }
            
            logger.info(f"RSI 计算成功 {symbol} {interval}, 周期: {period}, "
                       f"最新值: {latest_rsi:.2f}")
            
            return result
            
        except Exception as e:
            logger.error(f"RSI 计算失败 {symbol} {interval}: {str(e)}")
            raise
    
    def calculate_macd(self, symbol: str, interval: str,
                      fast_period: int = 12, slow_period: int = 26, 
                      signal_period: int = 9, market_type: str = "spot",
                      limit: Optional[int] = None,
                      start_time: Optional[int] = None,
                      end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        计算指数平滑异同移动平均线 (MACD)
        
        MACD 是一个趋势跟踪动量指标，显示两条移动平均线之间的关系。
        包含三个部分：
        - MACD 线：12 日 EMA - 26 日 EMA
        - 信号线：MACD 线的 9 日 EMA
        - 柱状图：MACD 线 - 信号线
        
        Args:
            symbol: 交易对或合约代码
            interval: K线时间间隔
            fast_period: 快速 EMA 周期，默认 12
            slow_period: 慢速 EMA 周期，默认 26
            signal_period: 信号线 EMA 周期，默认 9
            market_type: 市场类型
            limit: K线数量限制
            start_time: 开始时间戳
            end_time: 结束时间戳
            
        Returns:
            计算结果字典：
            {
                "indicator": "MACD",
                "symbol": "BTC-USDT",
                "interval": "1hour",
                "parameters": {
                    "fast_period": 12,
                    "slow_period": 26,
                    "signal_period": 9
                },
                "macd_line": [...],      # MACD 线值
                "signal_line": [...],    # 信号线值
                "histogram": [...],      # 柱状图值
                "timestamps": [...],     # 时间戳
                "latest_values": {
                    "macd": 123.45,
                    "signal": 120.30,
                    "histogram": 3.15
                }
            }
            
        Example:
            >>> # 使用默认参数计算 MACD
            >>> result = indicators.calculate_macd("BTC-USDT", "4hour")
            >>> if result["latest_values"]["histogram"] > 0:
            ...     print("MACD 柱状图为正，可能的上涨信号")
            >>> 
            >>> # 自定义参数
            >>> result = indicators.calculate_macd(
            ...     "ETH-USDT", "1day",
            ...     fast_period=10, slow_period=20, signal_period=5
            ... )
        """
        try:
            # 获取足够的K线数据
            min_required = self.config.get_min_klines_required("MACD") + slow_period
            if limit and limit < min_required:
                limit = min_required
            elif not limit:
                limit = max(100, min_required)
                
            # 获取K线数据
            df = self._get_kline_data(symbol, interval, market_type, limit, start_time, end_time)
            
            if len(df) < slow_period + signal_period:
                raise ValueError(f"数据不足：需要至少 {slow_period + signal_period} 条K线数据")
            
            # 计算 MACD
            close_prices = df['close'].values
            macd_line, signal_line, histogram = talib.MACD(
                close_prices,
                fastperiod=fast_period,
                slowperiod=slow_period,
                signalperiod=signal_period
            )
            
            # 移除 NaN 值
            valid_mask = ~(np.isnan(macd_line) | np.isnan(signal_line) | np.isnan(histogram))
            macd_line = macd_line[valid_mask]
            signal_line = signal_line[valid_mask]
            histogram = histogram[valid_mask]
            timestamps = df.index[valid_mask].astype(np.int64) // 10**9
            
            result = {
                "parameters": {
                    "fast_period": fast_period,
                    "slow_period": slow_period,
                    "signal_period": signal_period
                },
                "macd_line": macd_line.tolist(),
                "signal_line": signal_line.tolist(),
                "histogram": histogram.tolist(),
                "timestamps": timestamps.tolist(),
                "latest_values": {
                    "macd": float(macd_line[-1]) if len(macd_line) > 0 else None,
                    "signal": float(signal_line[-1]) if len(signal_line) > 0 else None,
                    "histogram": float(histogram[-1]) if len(histogram) > 0 else None
                }
            }
            
            logger.info(f"MACD 计算成功 {symbol} {interval}, "
                       f"最新柱状图: {result['latest_values']['histogram']:.4f}")
            
            return result
            
        except Exception as e:
            logger.error(f"MACD 计算失败 {symbol} {interval}: {str(e)}")
            raise
    
    def calculate_bollinger_bands(self, symbol: str, interval: str,
                                 period: int = 20, std_dev: float = 2.0,
                                 market_type: str = "spot",
                                 limit: Optional[int] = None,
                                 start_time: Optional[int] = None,
                                 end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        计算布林带 (Bollinger Bands)
        
        布林带由三条线组成：
        - 中轨：N 日简单移动平均线
        - 上轨：中轨 + K 倍的 N 日标准差
        - 下轨：中轨 - K 倍的 N 日标准差
        
        价格通常在布林带内波动，突破上下轨可能预示着趋势变化。
        
        Args:
            symbol: 交易对或合约代码
            interval: K线时间间隔
            period: 移动平均周期，默认 20
            std_dev: 标准差倍数，默认 2.0
            market_type: 市场类型
            limit: K线数量限制
            start_time: 开始时间戳
            end_time: 结束时间戳
            
        Returns:
            计算结果字典：
            {
                "indicator": "BBANDS",
                "symbol": "BTC-USDT",
                "interval": "1hour",
                "parameters": {
                    "period": 20,
                    "std_dev": 2.0
                },
                "upper_band": [...],     # 上轨值
                "middle_band": [...],    # 中轨值（SMA）
                "lower_band": [...],     # 下轨值
                "timestamps": [...],     # 时间戳
                "latest_values": {
                    "upper": 46500.00,
                    "middle": 45000.00,
                    "lower": 43500.00,
                    "price": 45200.00,   # 当前价格
                    "position": 0.6      # 价格在布林带中的相对位置
                },
                "bandwidth": 0.067      # 带宽（上轨-下轨）/中轨
            }
            
        Example:
            >>> # 计算标准布林带
            >>> result = indicators.calculate_bollinger_bands("BTC-USDT", "1hour")
            >>> position = result["latest_values"]["position"]
            >>> if position > 0.95:
            ...     print("价格接近上轨，可能回调")
            >>> elif position < 0.05:
            ...     print("价格接近下轨，可能反弹")
        """
        try:
            # 获取足够的K线数据
            min_required = self.config.get_min_klines_required("BBANDS") + period
            if limit and limit < min_required:
                limit = min_required
            elif not limit:
                limit = max(100, min_required)
                
            # 获取K线数据
            df = self._get_kline_data(symbol, interval, market_type, limit, start_time, end_time)
            
            if len(df) < period:
                raise ValueError(f"数据不足：需要至少 {period} 条K线数据")
            
            # 计算布林带
            close_prices = df['close'].values
            upper_band, middle_band, lower_band = talib.BBANDS(
                close_prices,
                timeperiod=period,
                nbdevup=std_dev,
                nbdevdn=std_dev,
                matype=0  # 使用 SMA
            )
            
            # 移除 NaN 值
            valid_mask = ~(np.isnan(upper_band) | np.isnan(middle_band) | np.isnan(lower_band))
            upper_band = upper_band[valid_mask]
            middle_band = middle_band[valid_mask]
            lower_band = lower_band[valid_mask]
            timestamps = df.index[valid_mask].astype(np.int64) // 10**9
            valid_close = close_prices[valid_mask]
            
            # 计算最新值和位置
            latest_price = float(valid_close[-1]) if len(valid_close) > 0 else None
            latest_upper = float(upper_band[-1]) if len(upper_band) > 0 else None
            latest_middle = float(middle_band[-1]) if len(middle_band) > 0 else None
            latest_lower = float(lower_band[-1]) if len(lower_band) > 0 else None
            
            # 计算价格在布林带中的相对位置 (0-1)
            position = None
            if latest_price and latest_upper and latest_lower:
                band_width = latest_upper - latest_lower
                if band_width > 0:
                    position = (latest_price - latest_lower) / band_width
            
            # 计算带宽指标
            bandwidth = None
            if latest_upper and latest_lower and latest_middle and latest_middle > 0:
                bandwidth = (latest_upper - latest_lower) / latest_middle
            
            result = {
                "parameters": {
                    "period": period,
                    "std_dev": std_dev
                },
                "upper_band": upper_band.tolist(),
                "middle_band": middle_band.tolist(),
                "lower_band": lower_band.tolist(),
                "timestamps": timestamps.tolist(),
                "latest_values": {
                    "upper": latest_upper,
                    "middle": latest_middle,
                    "lower": latest_lower,
                    "price": latest_price,
                    "position": float(position) if position is not None else None
                },
                "bandwidth": float(bandwidth) if bandwidth is not None else None
            }
            
            logger.info(f"布林带计算成功 {symbol} {interval}, "
                       f"价格位置: {position:.2f}, 带宽: {bandwidth:.4f}")
            
            return result
            
        except Exception as e:
            logger.error(f"布林带计算失败 {symbol} {interval}: {str(e)}")
            raise
    
    def calculate_kdj(self, symbol: str, interval: str,
                     fast_k_period: int = 9, slow_k_period: int = 3,
                     slow_d_period: int = 3, market_type: str = "spot",
                     limit: Optional[int] = None,
                     start_time: Optional[int] = None,
                     end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        计算 KDJ 随机指标
        
        KDJ 指标由三条线组成：
        - K线：快速确认线
        - D线：慢速主干线  
        - J线：方向敏感线，J = 3*K - 2*D
        
        常用于判断超买超卖和金叉死叉信号。
        
        Args:
            symbol: 交易对或合约代码
            interval: K线时间间隔
            fast_k_period: 快速K周期，默认 9
            slow_k_period: 慢速K周期，默认 3
            slow_d_period: 慢速D周期，默认 3
            market_type: 市场类型
            limit: K线数量限制
            start_time: 开始时间戳
            end_time: 结束时间戳
            
        Returns:
            计算结果字典，包含 K、D、J 三条线的值和交叉信号
            
        Example:
            >>> result = indicators.calculate_kdj("BTC-USDT", "1hour")
            >>> if result["latest_values"]["k"] > result["latest_values"]["d"]:
            ...     print("K线在D线上方")
        """
        try:
            # 获取足够的K线数据
            min_required = fast_k_period + slow_k_period + slow_d_period + 10
            if limit and limit < min_required:
                limit = min_required
            elif not limit:
                limit = max(100, min_required)
                
            # 获取K线数据
            df = self._get_kline_data(symbol, interval, market_type, limit, start_time, end_time)
            
            if len(df) < fast_k_period:
                raise ValueError(f"数据不足：需要至少 {fast_k_period} 条K线数据")
            
            # 使用 Stochastic 计算 K 和 D 值
            high_prices = df['high'].values
            low_prices = df['low'].values
            close_prices = df['close'].values
            
            k_values, d_values = talib.STOCH(
                high_prices, low_prices, close_prices,
                fastk_period=fast_k_period,
                slowk_period=slow_k_period,
                slowk_matype=0,
                slowd_period=slow_d_period,
                slowd_matype=0
            )
            
            # 计算 J 值：J = 3*K - 2*D
            j_values = 3 * k_values - 2 * d_values
            
            # 移除 NaN 值
            valid_mask = ~(np.isnan(k_values) | np.isnan(d_values) | np.isnan(j_values))
            k_values = k_values[valid_mask]
            d_values = d_values[valid_mask]
            j_values = j_values[valid_mask]
            timestamps = df.index[valid_mask].astype(np.int64) // 10**9
            
            result = {
                "parameters": {
                    "fast_k_period": fast_k_period,
                    "slow_k_period": slow_k_period,
                    "slow_d_period": slow_d_period
                },
                "k_values": k_values.tolist(),
                "d_values": d_values.tolist(),
                "j_values": j_values.tolist(),
                "timestamps": timestamps.tolist(),
                "latest_values": {
                    "k": float(k_values[-1]) if len(k_values) > 0 else None,
                    "d": float(d_values[-1]) if len(d_values) > 0 else None,
                    "j": float(j_values[-1]) if len(j_values) > 0 else None
                }
            }
            
            logger.info(f"KDJ 计算成功 {symbol} {interval}, "
                       f"K={result['latest_values']['k']:.2f}, "
                       f"D={result['latest_values']['d']:.2f}, "
                       f"J={result['latest_values']['j']:.2f}")
            
            return result
            
        except Exception as e:
            logger.error(f"KDJ 计算失败 {symbol} {interval}: {str(e)}")
            raise
    
    def calculate_moving_averages(self, symbol: str, interval: str,
                                 ma_type: str = "SMA", periods: List[int] = None,
                                 market_type: str = "spot",
                                 limit: Optional[int] = None,
                                 start_time: Optional[int] = None,
                                 end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        计算移动平均线（支持多种类型和多个周期）
        
        支持的移动平均类型：
        - SMA: 简单移动平均
        - EMA: 指数移动平均
        - WMA: 加权移动平均
        - DEMA: 双重指数移动平均
        - TEMA: 三重指数移动平均
        
        Args:
            symbol: 交易对或合约代码
            interval: K线时间间隔
            ma_type: 移动平均类型，默认 "SMA"
            periods: 周期列表，默认 [5, 10, 20, 50, 200]
            market_type: 市场类型
            limit: K线数量限制
            start_time: 开始时间戳
            end_time: 结束时间戳
            
        Returns:
            计算结果字典，包含各周期的移动平均值
            
        Example:
            >>> # 计算多条 SMA
            >>> result = indicators.calculate_moving_averages(
            ...     "BTC-USDT", "1hour", 
            ...     periods=[10, 20, 50]
            ... )
            >>> 
            >>> # 计算 EMA
            >>> result = indicators.calculate_moving_averages(
            ...     "ETH-USDT", "4hour",
            ...     ma_type="EMA",
            ...     periods=[12, 26]
            ... )
        """
        start_time_calc = time.time()
        
        if periods is None:
            periods = [5, 10, 20, 50, 200]
            
        try:
            # 获取足够的K线数据
            max_period = max(periods)
            min_required = max_period + 50  # 额外数据确保计算准确
            if limit and limit < min_required:
                limit = min_required
            elif not limit:
                limit = max(500, min_required)
                
            # 获取K线数据
            df = self._get_kline_data(symbol, interval, market_type, limit, start_time, end_time)
            
            if len(df) < max_period:
                raise ValueError(f"数据不足：需要至少 {max_period} 条K线数据")
            
            close_prices = df['close'].values
            results = {}
            
            # 计算各周期的移动平均
            for period in periods:
                if ma_type.upper() == "SMA":
                    ma_values = talib.SMA(close_prices, timeperiod=period)
                elif ma_type.upper() == "EMA":
                    ma_values = talib.EMA(close_prices, timeperiod=period)
                elif ma_type.upper() == "WMA":
                    ma_values = talib.WMA(close_prices, timeperiod=period)
                elif ma_type.upper() == "DEMA":
                    ma_values = talib.DEMA(close_prices, timeperiod=period)
                elif ma_type.upper() == "TEMA":
                    ma_values = talib.TEMA(close_prices, timeperiod=period)
                else:
                    raise ValueError(f"不支持的移动平均类型: {ma_type}")
                
                # 移除 NaN 值
                valid_mask = ~np.isnan(ma_values)
                ma_values = ma_values[valid_mask]
                timestamps = df.index[valid_mask].astype(np.int64) // 10**9
                
                results[f"ma{period}"] = {
                    "values": ma_values.tolist(),
                    "timestamps": timestamps.tolist(),
                    "latest_value": float(ma_values[-1]) if len(ma_values) > 0 else None
                }
            
            # 分析均线排列
            latest_values = {p: results[f"ma{p}"]["latest_value"] for p in periods if results[f"ma{p}"]["latest_value"]}
            sorted_periods = sorted(latest_values.keys())
            sorted_values = [latest_values[p] for p in sorted_periods]
            
            arrangement = "neutral"
            if len(sorted_values) >= 2:
                if all(sorted_values[i] > sorted_values[i+1] for i in range(len(sorted_values)-1)):
                    arrangement = "bullish"  # 多头排列
                elif all(sorted_values[i] < sorted_values[i+1] for i in range(len(sorted_values)-1)):
                    arrangement = "bearish"  # 空头排列
            
            result = {
                "ma_type": ma_type.upper(),
                "periods": periods,
                "data": results,
                "latest_price": float(close_prices[-1]),
                "arrangement": arrangement
            }
            
            logger.info(f"{ma_type.upper()} 计算成功 {symbol} {interval}, "
                       f"周期: {periods}, 排列: {arrangement}")
            
            return result
            
        except Exception as e:
            logger.error(f"{ma_type.upper()} 计算失败 {symbol} {interval}: {str(e)}")
            raise
    
    def calculate_atr(self, symbol: str, interval: str,
                     period: int = 14, market_type: str = "spot",
                     limit: Optional[int] = None,
                     start_time: Optional[int] = None,
                     end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        计算平均真实范围 (ATR)
        
        ATR 是衡量市场波动性的指标，不提供价格方向信息。
        常用于：
        - 设置止损位
        - 确定仓位大小
        - 识别市场波动性变化
        
        Args:
            symbol: 交易对或合约代码
            interval: K线时间间隔
            period: ATR 计算周期，默认 14
            market_type: 市场类型
            limit: K线数量限制
            start_time: 开始时间戳
            end_time: 结束时间戳
            
        Returns:
            计算结果字典，包含 ATR 值和波动性分析
            
        Example:
            >>> result = indicators.calculate_atr("BTC-USDT", "1hour")
            >>> atr = result["latest_value"]
            >>> price = result["latest_price"]
            >>> # 使用 ATR 设置止损
            >>> stop_loss = price - 2 * atr
        """
        try:
            # 获取足够的K线数据
            min_required = self.config.get_min_klines_required("ATR") + period
            if limit and limit < min_required:
                limit = min_required
            elif not limit:
                limit = max(100, min_required)
                
            # 获取K线数据
            df = self._get_kline_data(symbol, interval, market_type, limit, start_time, end_time)
            
            if len(df) < period + 1:
                raise ValueError(f"数据不足：需要至少 {period + 1} 条K线数据")
            
            # 计算 ATR
            high_prices = df['high'].values
            low_prices = df['low'].values
            close_prices = df['close'].values
            
            atr_values = talib.ATR(high_prices, low_prices, close_prices, timeperiod=period)
            
            # 移除 NaN 值
            valid_mask = ~np.isnan(atr_values)
            atr_values = atr_values[valid_mask]
            timestamps = df.index[valid_mask].astype(np.int64) // 10**9
            valid_close = close_prices[valid_mask]
            
            # 计算 ATR 占价格的百分比
            latest_atr = float(atr_values[-1]) if len(atr_values) > 0 else None
            latest_price = float(valid_close[-1]) if len(valid_close) > 0 else None
            atr_percentage = (latest_atr / latest_price * 100) if latest_atr and latest_price else None
            
            # 分析波动性
            volatility = "normal"
            if atr_percentage:
                if atr_percentage > 5:
                    volatility = "high"
                elif atr_percentage < 1:
                    volatility = "low"
            
            result = {
                "period": period,
                "values": atr_values.tolist(),
                "timestamps": timestamps.tolist(),
                "latest_value": latest_atr,
                "latest_price": latest_price,
                "atr_percentage": float(atr_percentage) if atr_percentage else None,
                "volatility": volatility
            }
            
            logger.info(f"ATR 计算成功 {symbol} {interval}, "
                       f"最新值: {latest_atr:.4f}, "
                       f"占价格比例: {atr_percentage:.2f}%, "
                       f"波动性: {volatility}")
            
            return result
            
        except Exception as e:
            logger.error(f"ATR 计算失败 {symbol} {interval}: {str(e)}")
            raise
    
    def calculate_vwap(self, symbol: str, interval: str,
                      market_type: str = "spot",
                      limit: Optional[int] = None,
                      start_time: Optional[int] = None,
                      end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        计算成交量加权平均价格 (VWAP)
        
        VWAP 是按成交量加权的平均价格，常用作：
        - 机构交易的基准价格
        - 判断当前价格相对于平均成交价的位置
        - 支撑和阻力位
        
        Args:
            symbol: 交易对或合约代码
            interval: K线时间间隔
            market_type: 市场类型
            limit: K线数量限制（VWAP 通常计算当日数据）
            start_time: 开始时间戳
            end_time: 结束时间戳
            
        Returns:
            计算结果字典，包含 VWAP 值和价格相对位置
            
        Example:
            >>> # 计算当日 VWAP
            >>> result = indicators.calculate_vwap("BTC-USDT", "5min", limit=288)
            >>> if result["price_position"] > 0:
            ...     print("当前价格高于 VWAP")
        """
        try:
            # VWAP 通常计算一天的数据
            if not limit:
                if interval in ["1min", "3min", "5min"]:
                    limit = 288  # 一天的5分钟K线数
                elif interval == "15min":
                    limit = 96
                elif interval == "30min":
                    limit = 48
                elif interval == "1hour":
                    limit = 24
                else:
                    limit = 100
                    
            # 获取K线数据
            df = self._get_kline_data(symbol, interval, market_type, limit, start_time, end_time)
            
            if len(df) < 1:
                raise ValueError("没有可用的K线数据")
            
            # 计算 VWAP
            # VWAP = Σ(Price × Volume) / Σ(Volume)
            # 使用典型价格 (High + Low + Close) / 3
            typical_price = (df['high'] + df['low'] + df['close']) / 3
            volume = df['volume']
            
            # 计算累计值
            cumulative_tpv = (typical_price * volume).cumsum()
            cumulative_volume = volume.cumsum()
            
            # 避免除零
            vwap_values = np.where(
                cumulative_volume > 0,
                cumulative_tpv / cumulative_volume,
                typical_price
            )
            
            timestamps = df.index.astype(np.int64) // 10**9
            close_prices = df['close'].values
            
            # 计算最新值
            latest_vwap = float(vwap_values[-1]) if len(vwap_values) > 0 else None
            latest_price = float(close_prices[-1]) if len(close_prices) > 0 else None
            
            # 计算价格相对于 VWAP 的位置
            price_position = None
            price_deviation = None
            if latest_vwap and latest_price:
                price_deviation = (latest_price - latest_vwap) / latest_vwap * 100
                price_position = "above" if latest_price > latest_vwap else "below" if latest_price < latest_vwap else "at"
            
            # 计算总成交量和成交额
            total_volume = float(volume.sum())
            total_value = float((typical_price * volume).sum())
            
            result = {
                "values": vwap_values.tolist(),
                "timestamps": timestamps.tolist(),
                "latest_vwap": latest_vwap,
                "latest_price": latest_price,
                "price_position": price_position,
                "price_deviation": float(price_deviation) if price_deviation is not None else None,
                "total_volume": total_volume,
                "total_value": total_value,
                "data_points": len(vwap_values)
            }
            
            logger.info(f"VWAP 计算成功 {symbol} {interval}, "
                       f"最新 VWAP: {latest_vwap:.2f}, "
                       f"价格位置: {price_position}, "
                       f"偏离度: {price_deviation:.2f}%")
            
            return result
            
        except Exception as e:
            logger.error(f"VWAP 计算失败 {symbol} {interval}: {str(e)}")
            raise
    
    def calculate_all_indicators(self, symbol: str, interval: str,
                               market_type: str = "spot",
                               indicators: List[str] = None,
                               limit: Optional[int] = None) -> Dict[str, Any]:
        """
        批量计算多个技术指标
        
        一次性计算多个指标，提高效率，避免重复获取K线数据。
        
        Args:
            symbol: 交易对或合约代码
            interval: K线时间间隔
            market_type: 市场类型
            indicators: 要计算的指标列表，默认计算常用指标
            limit: K线数量限制
            
        Returns:
            包含所有指标计算结果的字典
            
        Example:
            >>> # 计算常用指标
            >>> results = indicators.calculate_all_indicators(
            ...     "BTC-USDT", "1hour",
            ...     indicators=["RSI", "MACD", "BBANDS", "KDJ"]
            ... )
            >>> 
            >>> # 分析所有指标
            >>> for name, data in results["indicators"].items():
            ...     print(f"{name}: {data.get('latest_value', 'N/A')}")
        """
        if indicators is None:
            indicators = ["RSI", "MACD", "BBANDS", "KDJ", "ATR", "VWAP"]
            
        results = {
            "symbol": symbol,
            "interval": interval,
            "market_type": market_type,
            "timestamp": int(time.time()),
            "indicators": {},
            "errors": []
        }
        
        # 获取K线数据（一次性获取足够的数据）
        max_required = max([self.config.get_min_klines_required(ind) + 50 for ind in indicators])
        if not limit or limit < max_required:
            limit = max(200, max_required)
            
        try:
            # 计算各个指标
            for indicator in indicators:
                try:
                    if indicator.upper() == "RSI":
                        result = self.calculate_rsi(symbol, interval, market_type=market_type, limit=limit)
                    elif indicator.upper() == "MACD":
                        result = self.calculate_macd(symbol, interval, market_type=market_type, limit=limit)
                    elif indicator.upper() == "BBANDS":
                        result = self.calculate_bollinger_bands(symbol, interval, market_type=market_type, limit=limit)
                    elif indicator.upper() == "KDJ":
                        result = self.calculate_kdj(symbol, interval, market_type=market_type, limit=limit)
                    elif indicator.upper() == "ATR":
                        result = self.calculate_atr(symbol, interval, market_type=market_type, limit=limit)
                    elif indicator.upper() == "VWAP":
                        result = self.calculate_vwap(symbol, interval, market_type=market_type, limit=limit)
                    else:
                        results["errors"].append(f"不支持的指标: {indicator}")
                        continue
                        
                    results["indicators"][indicator.upper()] = result
                    
                except Exception as e:
                    logger.error(f"计算 {indicator} 失败: {str(e)}")
                    results["errors"].append(f"{indicator}: {str(e)}")
            
            logger.info(f"批量指标计算成功 {symbol} {interval}, "
                       f"指标数: {len(results['indicators'])}")
            
            return results
            
        except Exception as e:
            logger.error(f"批量指标计算失败 {symbol} {interval}: {str(e)}")
            raise
    
    # ======================= 新增技术指标方法 =======================
    
    def calculate_accumulation_distribution(self, symbol: str, interval: str, 
                                           market_type: str = "spot",
                                           limit: Optional[int] = None,
                                           start_time: Optional[int] = None,
                                           end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        计算累积/派发线 (Accumulation/Distribution Line)
        
        分析价格和成交量关系，评估资金流入和流出情况。
        """
        try:
            df = self._get_kline_data(symbol, interval, market_type, limit, start_time, end_time)
            
            # 计算 A/D Line
            ad_line = talib.AD(df['high'].values, df['low'].values, 
                             df['close'].values, df['volume'].values)
            
            # 移除 NaN 值
            valid_data = ~np.isnan(ad_line)
            timestamps = df.index[valid_data].astype(np.int64) // 10**9  # 转换为秒时间戳
            values = ad_line[valid_data].tolist()
            
            return {
                "timestamps": timestamps.tolist(),
                "values": values,
                "latest_value": values[-1] if values else None,
                "count": len(values)
            }
            
        except Exception as e:
            logger.error(f"计算 A/D Line 失败: {str(e)}")
            raise
    
    def calculate_aroon(self, symbol: str, interval: str, period: int = 14,
                       market_type: str = "spot",
                       limit: Optional[int] = None,
                       start_time: Optional[int] = None,
                       end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        计算阿隆指标 (Aroon)
        
        衡量趋势的强度和方向，识别趋势的开始或结束。
        """
        try:
            df = self._get_kline_data(symbol, interval, market_type, limit, start_time, end_time)
            
            # 计算 Aroon Up 和 Aroon Down
            aroon_down, aroon_up = talib.AROON(df['high'].values, df['low'].values, timeperiod=period)
            
            # 移除 NaN 值
            valid_data = ~np.isnan(aroon_up) & ~np.isnan(aroon_down)
            timestamps = df.index[valid_data].astype(np.int64) // 10**9  # 转换为秒时间戳
            aroon_up_values = aroon_up[valid_data].tolist()
            aroon_down_values = aroon_down[valid_data].tolist()
            
            return {
                "period": period,
                "timestamps": timestamps.tolist(),
                "aroon_up": aroon_up_values,
                "aroon_down": aroon_down_values,
                "latest_up": aroon_up_values[-1] if aroon_up_values else None,
                "latest_down": aroon_down_values[-1] if aroon_down_values else None,
                "count": len(aroon_up_values)
            }
            
        except Exception as e:
            logger.error(f"计算 Aroon 失败: {str(e)}")
            raise
    
    def calculate_adx(self, symbol: str, interval: str, period: int = 14,
                     market_type: str = "spot",
                     limit: Optional[int] = None,
                     start_time: Optional[int] = None,
                     end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        计算平均方向性指数 (ADX)
        
        评估趋势的强度，而不考虑其方向。
        """
        try:
            df = self._get_kline_data(symbol, interval, market_type, limit, start_time, end_time)
            
            # 计算 ADX
            adx = talib.ADX(df['high'].values, df['low'].values, df['close'].values, timeperiod=period)
            
            # 移除 NaN 值
            valid_data = ~np.isnan(adx)
            timestamps = df.index[valid_data].astype(np.int64) // 10**9  # 转换为秒时间戳
            values = adx[valid_data].tolist()
            
            return {
                "period": period,
                "timestamps": timestamps.tolist(),
                "values": values,
                "latest_value": values[-1] if values else None,
                "count": len(values)
            }
            
        except Exception as e:
            logger.error(f"计算 ADX 失败: {str(e)}")
            raise
    
    def calculate_awesome_oscillator(self, symbol: str, interval: str,
                                   fast_period: int = 5, slow_period: int = 34,
                                   market_type: str = "spot",
                                   limit: Optional[int] = None,
                                   start_time: Optional[int] = None,
                                   end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        计算极限振荡器 (Awesome Oscillator)
        
        比较近期和长期的移动平均值，评估市场动能。
        """
        try:
            df = self._get_kline_data(symbol, interval, market_type, limit, start_time, end_time)
            
            # 计算中价
            hl2 = (df['high'] + df['low']) / 2
            
            # 计算快速和慢速 SMA
            fast_sma = talib.SMA(hl2.values, timeperiod=fast_period)
            slow_sma = talib.SMA(hl2.values, timeperiod=slow_period)
            
            # 计算 AO = Fast SMA - Slow SMA
            ao = fast_sma - slow_sma
            
            # 移除 NaN 值
            valid_data = ~np.isnan(ao)
            timestamps = df.index[valid_data].astype(np.int64) // 10**9  # 转换为秒时间戳
            values = ao[valid_data].tolist()
            
            return {
                "fast_period": fast_period,
                "slow_period": slow_period,
                "timestamps": timestamps.tolist(),
                "values": values,
                "latest_value": values[-1] if values else None,
                "count": len(values)
            }
            
        except Exception as e:
            logger.error(f"计算 Awesome Oscillator 失败: {str(e)}")
            raise
    
    def calculate_bollinger_percent_b(self, symbol: str, interval: str, 
                                     period: int = 20, std_dev: float = 2.0,
                                     market_type: str = "spot",
                                     limit: Optional[int] = None,
                                     start_time: Optional[int] = None,
                                     end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        计算布林带 %B (Bollinger Bands %B)
        
        显示价格在布林带范围内的位置，帮助识别趋势和极端价格水平。
        """
        try:
            # 先计算标准布林带
            bb_result = self.calculate_bollinger_bands(symbol, interval, period, std_dev, 
                                                      market_type, limit, start_time, end_time)
            
            df = self._get_kline_data(symbol, interval, market_type, limit, start_time, end_time)
            
            # 计算 %B = (Price - Lower Band) / (Upper Band - Lower Band)
            upper = np.array(bb_result['upper_band'])
            lower = np.array(bb_result['lower_band'])
            close_prices = df['close'].values[-len(upper):]
            
            percent_b = (close_prices - lower) / (upper - lower) * 100
            
            return {
                "period": period,
                "std_dev": std_dev,
                "timestamps": bb_result['timestamps'],
                "values": percent_b.tolist(),
                "latest_value": percent_b[-1] if len(percent_b) > 0 else None,
                "count": len(percent_b)
            }
            
        except Exception as e:
            logger.error(f"计算 Bollinger %B 失败: {str(e)}")
            raise
    
    def calculate_bollinger_width(self, symbol: str, interval: str, 
                                 period: int = 20, std_dev: float = 2.0,
                                 market_type: str = "spot",
                                 limit: Optional[int] = None,
                                 start_time: Optional[int] = None,
                                 end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        计算布林带宽度 (Bollinger Bands Width)
        
        衡量布林带的宽度，反映市场波动性的变化。
        """
        try:
            # 先计算标准布林带
            bb_result = self.calculate_bollinger_bands(symbol, interval, period, std_dev, 
                                                      market_type, limit, start_time, end_time)
            
            # 计算宽度 = (Upper Band - Lower Band) / Middle Band
            upper = np.array(bb_result['upper_band'])
            lower = np.array(bb_result['lower_band'])
            middle = np.array(bb_result['middle_band'])
            
            width = (upper - lower) / middle * 100
            
            return {
                "period": period,
                "std_dev": std_dev,
                "timestamps": bb_result['timestamps'],
                "values": width.tolist(),
                "latest_value": width[-1] if len(width) > 0 else None,
                "count": len(width)
            }
            
        except Exception as e:
            logger.error(f"计算 Bollinger Width 失败: {str(e)}")
            raise
    
    def calculate_chaikin_money_flow(self, symbol: str, interval: str, period: int = 20,
                                    market_type: str = "spot",
                                    limit: Optional[int] = None,
                                    start_time: Optional[int] = None,
                                    end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        计算蔡金资金流量线 (Chaikin Money Flow)
        
        结合价格和成交量，评估资金流入和流出情况。
        """
        try:
            df = self._get_kline_data(symbol, interval, market_type, limit, start_time, end_time)
            
            # 计算 Money Flow Multiplier
            mfm = ((df['close'] - df['low']) - (df['high'] - df['close'])) / (df['high'] - df['low'])
            # 处理高低价相等的情况
            mfm = mfm.fillna(0)
            
            # 计算 Money Flow Volume
            mfv = mfm * df['volume']
            
            # 计算 CMF = Sum(MFV, period) / Sum(Volume, period)
            cmf_values = []
            timestamps = []
            
            for i in range(period - 1, len(df)):
                sum_mfv = mfv.iloc[i - period + 1:i + 1].sum()
                sum_volume = df['volume'].iloc[i - period + 1:i + 1].sum()
                
                if sum_volume != 0:
                    cmf = sum_mfv / sum_volume
                else:
                    cmf = 0
                    
                cmf_values.append(cmf)
                timestamps.append(int(df.index[i].timestamp()))
            
            return {
                "period": period,
                "timestamps": timestamps,
                "values": cmf_values,
                "latest_value": cmf_values[-1] if cmf_values else None,
                "count": len(cmf_values)
            }
            
        except Exception as e:
            logger.error(f"计算 Chaikin Money Flow 失败: {str(e)}")
            raise
    
    def calculate_chaikin_oscillator(self, symbol: str, interval: str,
                                   fast_period: int = 3, slow_period: int = 10,
                                   market_type: str = "spot",
                                   limit: Optional[int] = None,
                                   start_time: Optional[int] = None,
                                   end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        计算蔡金振荡器 (Chaikin Oscillator)
        
        基于累积/派发指标，衡量市场的买卖压力。
        """
        try:
            df = self._get_kline_data(symbol, interval, market_type, limit, start_time, end_time)
            
            # 先计算 A/D Line
            ad_line = talib.AD(df['high'].values, df['low'].values, 
                             df['close'].values, df['volume'].values)
            
            # 计算快速和慢速 EMA
            fast_ema = talib.EMA(ad_line, timeperiod=fast_period)
            slow_ema = talib.EMA(ad_line, timeperiod=slow_period)
            
            # 计算振荡器 = Fast EMA - Slow EMA
            oscillator = fast_ema - slow_ema
            
            # 移除 NaN 值
            valid_data = ~np.isnan(oscillator)
            timestamps = df.index[valid_data].astype(np.int64) // 10**9  # 转换为秒时间戳
            values = oscillator[valid_data].tolist()
            
            return {
                "fast_period": fast_period,
                "slow_period": slow_period,
                "timestamps": timestamps.tolist(),
                "values": values,
                "latest_value": values[-1] if values else None,
                "count": len(values)
            }
            
        except Exception as e:
            logger.error(f"计算 Chaikin Oscillator 失败: {str(e)}")
            raise
    
    def calculate_chande_momentum_oscillator(self, symbol: str, interval: str, period: int = 14,
                                           market_type: str = "spot",
                                           limit: Optional[int] = None,
                                           start_time: Optional[int] = None,
                                           end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        计算钱德动量振荡器 (Chande Momentum Oscillator)
        
        评估价格动量，识别超买或超卖状态。
        """
        try:
            df = self._get_kline_data(symbol, interval, market_type, limit, start_time, end_time)
            
            # 计算 CMO
            cmo = talib.CMO(df['close'].values, timeperiod=period)
            
            # 移除 NaN 值
            valid_data = ~np.isnan(cmo)
            timestamps = df.index[valid_data].astype(np.int64) // 10**9  # 转换为秒时间戳
            values = cmo[valid_data].tolist()
            
            return {
                "period": period,
                "timestamps": timestamps.tolist(),
                "values": values,
                "latest_value": values[-1] if values else None,
                "count": len(values)
            }
            
        except Exception as e:
            logger.error(f"计算 Chande Momentum Oscillator 失败: {str(e)}")
            raise
    
    def calculate_cci(self, symbol: str, interval: str, period: int = 14,
                     market_type: str = "spot",
                     limit: Optional[int] = None,
                     start_time: Optional[int] = None,
                     end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        计算商品通道指数 (Commodity Channel Index)
        
        评估价格偏离其平均值的程度，识别超买或超卖状态。
        """
        try:
            df = self._get_kline_data(symbol, interval, market_type, limit, start_time, end_time)
            
            # 计算 CCI
            cci = talib.CCI(df['high'].values, df['low'].values, df['close'].values, timeperiod=period)
            
            # 移除 NaN 值
            valid_data = ~np.isnan(cci)
            timestamps = df.index[valid_data].astype(np.int64) // 10**9  # 转换为秒时间戳
            values = cci[valid_data].tolist()
            
            return {
                "period": period,
                "timestamps": timestamps.tolist(),
                "values": values,
                "latest_value": values[-1] if values else None,
                "count": len(values)
            }
            
        except Exception as e:
            logger.error(f"计算 CCI 失败: {str(e)}")
            raise
    
    def calculate_money_flow_index(self, symbol: str, interval: str, period: int = 14,
                                  market_type: str = "spot",
                                  limit: Optional[int] = None,
                                  start_time: Optional[int] = None,
                                  end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        计算资金流量指数 (Money Flow Index)
        
        结合价格和成交量，评估资金流入和流出，识别超买或超卖状态。
        """
        try:
            df = self._get_kline_data(symbol, interval, market_type, limit, start_time, end_time)
            
            # 计算 MFI
            mfi = talib.MFI(df['high'].values, df['low'].values, df['close'].values, 
                           df['volume'].values, timeperiod=period)
            
            # 移除 NaN 值
            valid_data = ~np.isnan(mfi)
            timestamps = df.index[valid_data].astype(np.int64) // 10**9  # 转换为秒时间戳
            values = mfi[valid_data].tolist()
            
            return {
                "period": period,
                "timestamps": timestamps.tolist(),
                "values": values,
                "latest_value": values[-1] if values else None,
                "count": len(values)
            }
            
        except Exception as e:
            logger.error(f"计算 MFI 失败: {str(e)}")
            raise
    
    def calculate_obv(self, symbol: str, interval: str,
                     market_type: str = "spot",
                     limit: Optional[int] = None,
                     start_time: Optional[int] = None,
                     end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        计算平衡成交量 (On Balance Volume)
        
        通过累积每日的成交量，根据价格上涨或下跌方向进行加减，
        旨在反映资金流入和流出情况，以预测价格走势。
        """
        try:
            df = self._get_kline_data(symbol, interval, market_type, limit, start_time, end_time)
            
            # 计算 OBV
            obv = talib.OBV(df['close'].values, df['volume'].values)
            
            # 移除 NaN 值
            valid_data = ~np.isnan(obv)
            timestamps = df.index[valid_data].astype(np.int64) // 10**9  # 转换为秒时间戳
            values = obv[valid_data].tolist()
            
            return {
                "timestamps": timestamps.tolist(),
                "values": values,
                "latest_value": values[-1] if values else None,
                "count": len(values)
            }
            
        except Exception as e:
            logger.error(f"计算 OBV 失败: {str(e)}")
            raise
    
    def calculate_parabolic_sar(self, symbol: str, interval: str,
                               acceleration: float = 0.02, maximum: float = 0.2,
                               market_type: str = "spot",
                               limit: Optional[int] = None,
                               start_time: Optional[int] = None,
                               end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        计算抛物线转向指标 (Parabolic SAR)
        
        根据价格和时间的关系，提供趋势反转信号和止损位。
        """
        try:
            df = self._get_kline_data(symbol, interval, market_type, limit, start_time, end_time)
            
            # 计算 SAR
            sar = talib.SAR(df['high'].values, df['low'].values, 
                           acceleration=acceleration, maximum=maximum)
            
            # 移除 NaN 值
            valid_data = ~np.isnan(sar)
            timestamps = df.index[valid_data].astype(np.int64) // 10**9  # 转换为秒时间戳
            values = sar[valid_data].tolist()
            
            return {
                "acceleration": acceleration,
                "maximum": maximum,
                "timestamps": timestamps.tolist(),
                "values": values,
                "latest_value": values[-1] if values else None,
                "count": len(values)
            }
            
        except Exception as e:
            logger.error(f"计算 Parabolic SAR 失败: {str(e)}")
            raise
    
    def calculate_momentum(self, symbol: str, interval: str, period: int = 10,
                          market_type: str = "spot",
                          limit: Optional[int] = None,
                          start_time: Optional[int] = None,
                          end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        计算动量指标 (Momentum)
        
        衡量价格变动的速度，识别趋势的强弱和可能的反转点。
        """
        try:
            df = self._get_kline_data(symbol, interval, market_type, limit, start_time, end_time)
            
            # 计算 Momentum
            momentum = talib.MOM(df['close'].values, timeperiod=period)
            
            # 移除 NaN 值
            valid_data = ~np.isnan(momentum)
            timestamps = df.index[valid_data].astype(np.int64) // 10**9  # 转换为秒时间戳
            values = momentum[valid_data].tolist()
            
            return {
                "period": period,
                "timestamps": timestamps.tolist(),
                "values": values,
                "latest_value": values[-1] if values else None,
                "count": len(values)
            }
            
        except Exception as e:
            logger.error(f"计算 Momentum 失败: {str(e)}")
            raise
    
    def calculate_roc(self, symbol: str, interval: str, period: int = 10,
                     market_type: str = "spot",
                     limit: Optional[int] = None,
                     start_time: Optional[int] = None,
                     end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        计算变动率 (Rate of Change)
        
        计算当前价格与过去某一周期价格之间的百分比变化，
        评估价格变动的速度和趋势强度。
        """
        try:
            df = self._get_kline_data(symbol, interval, market_type, limit, start_time, end_time)
            
            # 计算 ROC
            roc = talib.ROC(df['close'].values, timeperiod=period)
            
            # 移除 NaN 值
            valid_data = ~np.isnan(roc)
            timestamps = df.index[valid_data].astype(np.int64) // 10**9  # 转换为秒时间戳
            values = roc[valid_data].tolist()
            
            return {
                "period": period,
                "timestamps": timestamps.tolist(),
                "values": values,
                "latest_value": values[-1] if values else None,
                "count": len(values)
            }
            
        except Exception as e:
            logger.error(f"计算 ROC 失败: {str(e)}")
            raise
    
    def calculate_stochastic(self, symbol: str, interval: str, 
                           fastk_period: int = 5, slowk_period: int = 3, slowd_period: int = 3,
                           market_type: str = "spot",
                           limit: Optional[int] = None,
                           start_time: Optional[int] = None,
                           end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        计算随机指标 (Stochastic)
        
        通过比较收盘价与一段时间内的最高价和最低价，
        评估价格的动量和超买超卖状态。
        """
        try:
            df = self._get_kline_data(symbol, interval, market_type, limit, start_time, end_time)
            
            # 计算 Stochastic
            slowk, slowd = talib.STOCH(df['high'].values, df['low'].values, df['close'].values,
                                      fastk_period=fastk_period, slowk_period=slowk_period, 
                                      slowd_period=slowd_period)
            
            # 移除 NaN 值
            valid_data = ~np.isnan(slowk) & ~np.isnan(slowd)
            timestamps = df.index[valid_data].astype(np.int64) // 10**9  # 转换为秒时间戳
            k_values = slowk[valid_data].tolist()
            d_values = slowd[valid_data].tolist()
            
            return {
                "fastk_period": fastk_period,
                "slowk_period": slowk_period,
                "slowd_period": slowd_period,
                "timestamps": timestamps.tolist(),
                "k_values": k_values,
                "d_values": d_values,
                "latest_k": k_values[-1] if k_values else None,
                "latest_d": d_values[-1] if d_values else None,
                "count": len(k_values)
            }
            
        except Exception as e:
            logger.error(f"计算 Stochastic 失败: {str(e)}")
            raise
    
    def calculate_stochastic_rsi(self, symbol: str, interval: str, 
                               timeperiod: int = 14, fastk_period: int = 5, 
                               fastd_period: int = 3,
                               market_type: str = "spot",
                               limit: Optional[int] = None,
                               start_time: Optional[int] = None,
                               end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        计算随机 RSI (Stochastic RSI)
        
        计算 RSI 值的随机变化，识别超买或超卖区域。
        """
        try:
            df = self._get_kline_data(symbol, interval, market_type, limit, start_time, end_time)
            
            # 计算 Stochastic RSI
            fastk, fastd = talib.STOCHRSI(df['close'].values, timeperiod=timeperiod,
                                         fastk_period=fastk_period, fastd_period=fastd_period)
            
            # 移除 NaN 值
            valid_data = ~np.isnan(fastk) & ~np.isnan(fastd)
            timestamps = df.index[valid_data].astype(np.int64) // 10**9  # 转换为秒时间戳
            k_values = fastk[valid_data].tolist()
            d_values = fastd[valid_data].tolist()
            
            return {
                "timeperiod": timeperiod,
                "fastk_period": fastk_period,
                "fastd_period": fastd_period,
                "timestamps": timestamps.tolist(),
                "k_values": k_values,
                "d_values": d_values,
                "latest_k": k_values[-1] if k_values else None,
                "latest_d": d_values[-1] if d_values else None,
                "count": len(k_values)
            }
            
        except Exception as e:
            logger.error(f"计算 Stochastic RSI 失败: {str(e)}")
            raise
    
    def calculate_williams_r(self, symbol: str, interval: str, period: int = 14,
                           market_type: str = "spot",
                           limit: Optional[int] = None,
                           start_time: Optional[int] = None,
                           end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        计算威廉姆斯 %R (Williams %R)
        
        衡量价格相对于最高价和最低价的相对位置，识别超买或超卖状态。
        """
        try:
            df = self._get_kline_data(symbol, interval, market_type, limit, start_time, end_time)
            
            # 计算 Williams %R
            willr = talib.WILLR(df['high'].values, df['low'].values, df['close'].values, timeperiod=period)
            
            # 移除 NaN 值
            valid_data = ~np.isnan(willr)
            timestamps = df.index[valid_data].astype(np.int64) // 10**9  # 转换为秒时间戳
            values = willr[valid_data].tolist()
            
            return {
                "period": period,
                "timestamps": timestamps.tolist(),
                "values": values,
                "latest_value": values[-1] if values else None,
                "count": len(values)
            }
            
        except Exception as e:
            logger.error(f"计算 Williams %R 失败: {str(e)}")
            raise
    
    def calculate_ultimate_oscillator(self, symbol: str, interval: str,
                                    period1: int = 7, period2: int = 14, period3: int = 28,
                                    market_type: str = "spot",
                                    limit: Optional[int] = None,
                                    start_time: Optional[int] = None,
                                    end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        计算终极振荡器 (Ultimate Oscillator)
        
        通过结合不同时间周期的买入压力，减少虚假信号，
        从而提供更可靠的超买和超卖信号。
        """
        try:
            df = self._get_kline_data(symbol, interval, market_type, limit, start_time, end_time)
            
            # 计算 Ultimate Oscillator
            ultosc = talib.ULTOSC(df['high'].values, df['low'].values, df['close'].values,
                                 timeperiod1=period1, timeperiod2=period2, timeperiod3=period3)
            
            # 移除 NaN 值
            valid_data = ~np.isnan(ultosc)
            timestamps = df.index[valid_data].astype(np.int64) // 10**9  # 转换为秒时间戳
            values = ultosc[valid_data].tolist()
            
            return {
                "period1": period1,
                "period2": period2,
                "period3": period3,
                "timestamps": timestamps.tolist(),
                "values": values,
                "latest_value": values[-1] if values else None,
                "count": len(values)
            }
            
        except Exception as e:
            logger.error(f"计算 Ultimate Oscillator 失败: {str(e)}")
            raise
    
    def calculate_triple_ema(self, symbol: str, interval: str, period: int = 20,
                           market_type: str = "spot",
                           limit: Optional[int] = None,
                           start_time: Optional[int] = None,
                           end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        计算三重指数移动平均线 (Triple EMA)
        
        对价格数据进行三次指数平滑处理的移动平均线，
        旨在减少滞后效应，更准确地反映价格趋势变化。
        """
        try:
            df = self._get_kline_data(symbol, interval, market_type, limit, start_time, end_time)
            
            # 计算 TEMA
            tema = talib.TEMA(df['close'].values, timeperiod=period)
            
            # 移除 NaN 值
            valid_data = ~np.isnan(tema)
            timestamps = df.index[valid_data].astype(np.int64) // 10**9  # 转换为秒时间戳
            values = tema[valid_data].tolist()
            
            return {
                "period": period,
                "timestamps": timestamps.tolist(),
                "values": values,
                "latest_value": values[-1] if values else None,
                "count": len(values)
            }
            
        except Exception as e:
            logger.error(f"计算 Triple EMA 失败: {str(e)}")
            raise
    
    def calculate_vwma(self, symbol: str, interval: str, period: int = 20,
                      market_type: str = "spot",
                      limit: Optional[int] = None,
                      start_time: Optional[int] = None,
                      end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        计算交易量加权移动平均线 (Volume Weighted Moving Average)
        
        结合价格和成交量计算的移动平均线，更准确反映真实趋势。
        """
        try:
            df = self._get_kline_data(symbol, interval, market_type, limit, start_time, end_time)
            
            # 计算 VWMA
            vwma_values = []
            timestamps = []
            
            for i in range(period - 1, len(df)):
                price_volume = (df['close'].iloc[i - period + 1:i + 1] * 
                               df['volume'].iloc[i - period + 1:i + 1]).sum()
                total_volume = df['volume'].iloc[i - period + 1:i + 1].sum()
                
                if total_volume > 0:
                    vwma = price_volume / total_volume
                    vwma_values.append(vwma)
                    timestamps.append(int(df.index[i].timestamp()))
            
            return {
                "period": period,
                "timestamps": timestamps,
                "values": vwma_values,
                "latest_value": vwma_values[-1] if vwma_values else None,
                "count": len(vwma_values)
            }
            
        except Exception as e:
            logger.error(f"计算 VWMA 失败: {str(e)}")
            raise
    
    def calculate_volume_oscillator(self, symbol: str, interval: str,
                                   fast_period: int = 5, slow_period: int = 10,
                                   market_type: str = "spot",
                                   limit: Optional[int] = None,
                                   start_time: Optional[int] = None,
                                   end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        计算成交量振荡器 (Volume Oscillator)
        
        衡量不同时间段成交量的变化，用于确认趋势。
        """
        try:
            df = self._get_kline_data(symbol, interval, market_type, limit, start_time, end_time)
            
            # 计算快速和慢速成交量移动平均
            fast_vol_ma = talib.SMA(df['volume'].values, timeperiod=fast_period)
            slow_vol_ma = talib.SMA(df['volume'].values, timeperiod=slow_period)
            
            # 计算振荡器 = (Fast MA - Slow MA) / Slow MA * 100
            vol_osc = (fast_vol_ma - slow_vol_ma) / slow_vol_ma * 100
            
            # 移除 NaN 值
            valid_data = ~np.isnan(vol_osc)
            timestamps = df.index[valid_data].astype(np.int64) // 10**9  # 转换为秒时间戳
            values = vol_osc[valid_data].tolist()
            
            return {
                "fast_period": fast_period,
                "slow_period": slow_period,
                "timestamps": timestamps.tolist(),
                "values": values,
                "latest_value": values[-1] if values else None,
                "count": len(values)
            }
            
        except Exception as e:
            logger.error(f"计算 Volume Oscillator 失败: {str(e)}")
            raise
    
    def calculate_vortex_indicator(self, symbol: str, interval: str, period: int = 14,
                                  market_type: str = "spot",
                                  limit: Optional[int] = None,
                                  start_time: Optional[int] = None,
                                  end_time: Optional[int] = None) -> Dict[str, Any]:
        """
        计算涡轮指标 (Vortex Indicator)
        
        基于价格的高点、低点和收盘价，检测趋势方向和强度。
        """
        try:
            df = self._get_kline_data(symbol, interval, market_type, limit, start_time, end_time)
            
            # 计算 True Range
            tr = np.maximum(df['high'] - df['low'],
                           np.maximum(abs(df['high'] - df['close'].shift(1)),
                                     abs(df['low'] - df['close'].shift(1))))
            
            # 计算 VM+, VM-
            vm_plus = abs(df['high'] - df['low'].shift(1))
            vm_minus = abs(df['low'] - df['high'].shift(1))
            
            # 计算 VI+ 和 VI-
            vi_plus_values = []
            vi_minus_values = []
            timestamps = []
            
            for i in range(period, len(df)):
                sum_vm_plus = vm_plus.iloc[i - period + 1:i + 1].sum()
                sum_vm_minus = vm_minus.iloc[i - period + 1:i + 1].sum()
                sum_tr = tr.iloc[i - period + 1:i + 1].sum()
                
                if sum_tr > 0:
                    vi_plus = sum_vm_plus / sum_tr
                    vi_minus = sum_vm_minus / sum_tr
                    
                    vi_plus_values.append(vi_plus)
                    vi_minus_values.append(vi_minus)
                    timestamps.append(int(df.index[i].timestamp()))
            
            return {
                "period": period,
                "timestamps": timestamps,
                "vi_plus": vi_plus_values,
                "vi_minus": vi_minus_values,
                "latest_vi_plus": vi_plus_values[-1] if vi_plus_values else None,
                "latest_vi_minus": vi_minus_values[-1] if vi_minus_values else None,
                "count": len(vi_plus_values)
            }
            
        except Exception as e:
            logger.error(f"计算 Vortex Indicator 失败: {str(e)}")
            raise