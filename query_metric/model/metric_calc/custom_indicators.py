"""
Custom implementations of technical indicators to replace TA-Lib dependency

This module provides pure Python/NumPy implementations of various technical indicators
that were previously computed using TA-Lib. All functions maintain the same interface
and return format as the original TA-Lib functions.
"""

import numpy as np
from typing import Tuple, Optional


def RSI(close: np.ndarray, timeperiod: int = 14) -> np.ndarray:
    """
    Calculate Relative Strength Index (RSI)
    
    Args:
        close: Array of closing prices
        timeperiod: Period for RSI calculation (default: 14)
        
    Returns:
        Array of RSI values
    """
    deltas = np.diff(close)
    seed = deltas[:timeperiod+1]
    up = seed[seed >= 0].sum() / timeperiod
    down = -seed[seed < 0].sum() / timeperiod
    rs = up / down if down != 0 else 0
    rsi = np.zeros_like(close)
    rsi[:timeperiod] = np.nan
    rsi[timeperiod] = 100. - 100. / (1. + rs)
    
    for i in range(timeperiod + 1, len(close)):
        delta = deltas[i - 1]
        if delta > 0:
            upval = delta
            downval = 0.
        else:
            upval = 0.
            downval = -delta
            
        up = (up * (timeperiod - 1) + upval) / timeperiod
        down = (down * (timeperiod - 1) + downval) / timeperiod
        
        rs = up / down if down != 0 else 0
        rsi[i] = 100. - 100. / (1. + rs)
        
    return rsi


def MACD(close: np.ndarray, fastperiod: int = 12, slowperiod: int = 26, 
         signalperiod: int = 9) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    Calculate MACD (Moving Average Convergence/Divergence)
    
    Args:
        close: Array of closing prices
        fastperiod: Fast EMA period (default: 12)
        slowperiod: Slow EMA period (default: 26)
        signalperiod: Signal line EMA period (default: 9)
        
    Returns:
        Tuple of (macd_line, signal_line, histogram)
    """
    # Calculate EMAs
    ema_fast = EMA(close, timeperiod=fastperiod)
    ema_slow = EMA(close, timeperiod=slowperiod)
    
    # MACD line
    macd_line = ema_fast - ema_slow
    
    # Signal line (EMA of MACD line)
    signal_line = EMA(macd_line[~np.isnan(macd_line)], timeperiod=signalperiod)
    
    # Align signal line with macd line
    signal_full = np.full_like(macd_line, np.nan)
    valid_idx = ~np.isnan(macd_line)
    signal_full[valid_idx] = np.concatenate([
        np.full(len(macd_line[valid_idx]) - len(signal_line), np.nan),
        signal_line
    ])
    
    # Histogram
    histogram = macd_line - signal_full
    
    return macd_line, signal_full, histogram


def BBANDS(close: np.ndarray, timeperiod: int = 20, nbdevup: float = 2.0, 
           nbdevdn: float = 2.0, matype: int = 0) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    Calculate Bollinger Bands
    
    Args:
        close: Array of closing prices
        timeperiod: Period for moving average (default: 20)
        nbdevup: Number of standard deviations for upper band (default: 2.0)
        nbdevdn: Number of standard deviations for lower band (default: 2.0)
        matype: Moving average type (0=SMA, default: 0)
        
    Returns:
        Tuple of (upper_band, middle_band, lower_band)
    """
    # Calculate middle band (SMA)
    middle_band = SMA(close, timeperiod=timeperiod)
    
    # Calculate standard deviation
    std_dev = np.zeros_like(close)
    std_dev[:] = np.nan
    
    for i in range(timeperiod - 1, len(close)):
        std_dev[i] = np.std(close[i - timeperiod + 1:i + 1], ddof=0)
    
    # Calculate bands
    upper_band = middle_band + (std_dev * nbdevup)
    lower_band = middle_band - (std_dev * nbdevdn)
    
    return upper_band, middle_band, lower_band


def STOCH(high: np.ndarray, low: np.ndarray, close: np.ndarray,
          fastk_period: int = 5, slowk_period: int = 3, slowk_matype: int = 0,
          slowd_period: int = 3, slowd_matype: int = 0) -> Tuple[np.ndarray, np.ndarray]:
    """
    Calculate Stochastic oscillator
    
    Args:
        high: Array of high prices
        low: Array of low prices
        close: Array of closing prices
        fastk_period: Period for %K calculation (default: 5)
        slowk_period: Period for %K smoothing (default: 3)
        slowk_matype: MA type for %K smoothing (default: 0=SMA)
        slowd_period: Period for %D calculation (default: 3)
        slowd_matype: MA type for %D calculation (default: 0=SMA)
        
    Returns:
        Tuple of (slowk, slowd)
    """
    # Calculate Fast %K
    fastk = np.zeros_like(close)
    fastk[:] = np.nan
    
    for i in range(fastk_period - 1, len(close)):
        highest = np.max(high[i - fastk_period + 1:i + 1])
        lowest = np.min(low[i - fastk_period + 1:i + 1])
        
        if highest != lowest:
            fastk[i] = 100 * (close[i] - lowest) / (highest - lowest)
        else:
            fastk[i] = 50  # Default when highest == lowest
    
    # Calculate Slow %K (smoothed Fast %K)
    slowk = SMA(fastk, timeperiod=slowk_period)
    
    # Calculate Slow %D (smoothed Slow %K)
    slowd = SMA(slowk, timeperiod=slowd_period)
    
    return slowk, slowd


def SMA(close: np.ndarray, timeperiod: int = 30) -> np.ndarray:
    """
    Calculate Simple Moving Average
    
    Args:
        close: Array of closing prices
        timeperiod: Period for SMA calculation
        
    Returns:
        Array of SMA values
    """
    sma = np.zeros_like(close)
    sma[:] = np.nan
    
    for i in range(timeperiod - 1, len(close)):
        sma[i] = np.mean(close[i - timeperiod + 1:i + 1])
        
    return sma


def EMA(close: np.ndarray, timeperiod: int = 30) -> np.ndarray:
    """
    Calculate Exponential Moving Average
    
    Args:
        close: Array of closing prices
        timeperiod: Period for EMA calculation
        
    Returns:
        Array of EMA values
    """
    ema = np.zeros_like(close)
    ema[:] = np.nan
    
    # Start with SMA for the first value
    sma_initial = np.mean(close[:timeperiod])
    ema[timeperiod - 1] = sma_initial
    
    # Calculate multiplier
    multiplier = 2.0 / (timeperiod + 1)
    
    # Calculate EMA
    for i in range(timeperiod, len(close)):
        ema[i] = (close[i] * multiplier) + (ema[i - 1] * (1 - multiplier))
        
    return ema


def WMA(close: np.ndarray, timeperiod: int = 30) -> np.ndarray:
    """
    Calculate Weighted Moving Average
    
    Args:
        close: Array of closing prices
        timeperiod: Period for WMA calculation
        
    Returns:
        Array of WMA values
    """
    wma = np.zeros_like(close)
    wma[:] = np.nan
    
    # Create weights
    weights = np.arange(1, timeperiod + 1)
    
    for i in range(timeperiod - 1, len(close)):
        wma[i] = np.sum(close[i - timeperiod + 1:i + 1] * weights) / np.sum(weights)
        
    return wma


def DEMA(close: np.ndarray, timeperiod: int = 30) -> np.ndarray:
    """
    Calculate Double Exponential Moving Average
    
    Args:
        close: Array of closing prices
        timeperiod: Period for DEMA calculation
        
    Returns:
        Array of DEMA values
    """
    ema1 = EMA(close, timeperiod)
    ema2 = EMA(ema1, timeperiod)
    dema = 2 * ema1 - ema2
    
    return dema


def TEMA(close: np.ndarray, timeperiod: int = 30) -> np.ndarray:
    """
    Calculate Triple Exponential Moving Average
    
    Args:
        close: Array of closing prices
        timeperiod: Period for TEMA calculation
        
    Returns:
        Array of TEMA values
    """
    ema1 = EMA(close, timeperiod)
    ema2 = EMA(ema1, timeperiod)
    ema3 = EMA(ema2, timeperiod)
    tema = 3 * ema1 - 3 * ema2 + ema3
    
    return tema


def ATR(high: np.ndarray, low: np.ndarray, close: np.ndarray, timeperiod: int = 14) -> np.ndarray:
    """
    Calculate Average True Range
    
    Args:
        high: Array of high prices
        low: Array of low prices
        close: Array of closing prices
        timeperiod: Period for ATR calculation
        
    Returns:
        Array of ATR values
    """
    # Calculate True Range
    tr = np.zeros_like(close)
    tr[0] = high[0] - low[0]
    
    for i in range(1, len(close)):
        hl = high[i] - low[i]
        hc = abs(high[i] - close[i - 1])
        lc = abs(low[i] - close[i - 1])
        tr[i] = max(hl, hc, lc)
    
    # Calculate ATR (smoothed average of TR)
    atr = np.zeros_like(close)
    atr[:] = np.nan
    
    # Initial ATR is simple average
    atr[timeperiod - 1] = np.mean(tr[:timeperiod])
    
    # Subsequent values use smoothing
    for i in range(timeperiod, len(close)):
        atr[i] = (atr[i - 1] * (timeperiod - 1) + tr[i]) / timeperiod
        
    return atr


def AD(high: np.ndarray, low: np.ndarray, close: np.ndarray, volume: np.ndarray) -> np.ndarray:
    """
    Calculate Accumulation/Distribution Line
    
    Args:
        high: Array of high prices
        low: Array of low prices
        close: Array of closing prices
        volume: Array of volume data
        
    Returns:
        Array of A/D line values
    """
    ad = np.zeros_like(close)
    
    for i in range(len(close)):
        if high[i] != low[i]:
            mfm = ((close[i] - low[i]) - (high[i] - close[i])) / (high[i] - low[i])
        else:
            mfm = 0
            
        if i == 0:
            ad[i] = mfm * volume[i]
        else:
            ad[i] = ad[i - 1] + mfm * volume[i]
            
    return ad


def AROON(high: np.ndarray, low: np.ndarray, timeperiod: int = 14) -> Tuple[np.ndarray, np.ndarray]:
    """
    Calculate Aroon indicator
    
    Args:
        high: Array of high prices
        low: Array of low prices
        timeperiod: Period for Aroon calculation
        
    Returns:
        Tuple of (aroon_down, aroon_up)
    """
    aroon_up = np.zeros_like(high)
    aroon_down = np.zeros_like(high)
    aroon_up[:] = np.nan
    aroon_down[:] = np.nan
    
    for i in range(timeperiod, len(high)):
        # Find the index of highest high and lowest low in the period
        period_high = high[i - timeperiod + 1:i + 1]
        period_low = low[i - timeperiod + 1:i + 1]
        
        high_idx = np.argmax(period_high)
        low_idx = np.argmin(period_low)
        
        # Calculate Aroon values
        aroon_up[i] = ((timeperiod - (timeperiod - 1 - high_idx)) / timeperiod) * 100
        aroon_down[i] = ((timeperiod - (timeperiod - 1 - low_idx)) / timeperiod) * 100
        
    return aroon_down, aroon_up


def ADX(high: np.ndarray, low: np.ndarray, close: np.ndarray, timeperiod: int = 14) -> np.ndarray:
    """
    Calculate Average Directional Index
    
    Args:
        high: Array of high prices
        low: Array of low prices
        close: Array of closing prices
        timeperiod: Period for ADX calculation
        
    Returns:
        Array of ADX values
    """
    # Calculate True Range
    tr = np.zeros_like(close)
    tr[0] = high[0] - low[0]
    
    # Calculate directional movements
    plus_dm = np.zeros_like(close)
    minus_dm = np.zeros_like(close)
    
    for i in range(1, len(close)):
        hl = high[i] - low[i]
        hc = abs(high[i] - close[i - 1])
        lc = abs(low[i] - close[i - 1])
        tr[i] = max(hl, hc, lc)
        
        # Directional movements
        up_move = high[i] - high[i - 1]
        down_move = low[i - 1] - low[i]
        
        if up_move > down_move and up_move > 0:
            plus_dm[i] = up_move
        else:
            plus_dm[i] = 0
            
        if down_move > up_move and down_move > 0:
            minus_dm[i] = down_move
        else:
            minus_dm[i] = 0
    
    # Smooth the values
    atr = ATR(high, low, close, timeperiod)
    
    # Smooth directional movements
    plus_di = np.zeros_like(close)
    minus_di = np.zeros_like(close)
    plus_di[:] = np.nan
    minus_di[:] = np.nan
    
    # Initial values
    plus_di[timeperiod - 1] = 100 * np.sum(plus_dm[:timeperiod]) / np.sum(tr[:timeperiod])
    minus_di[timeperiod - 1] = 100 * np.sum(minus_dm[:timeperiod]) / np.sum(tr[:timeperiod])
    
    for i in range(timeperiod, len(close)):
        plus_di[i] = 100 * ((plus_di[i-1] * (timeperiod - 1) + plus_dm[i]) / timeperiod) / atr[i] if atr[i] != 0 else 0
        minus_di[i] = 100 * ((minus_di[i-1] * (timeperiod - 1) + minus_dm[i]) / timeperiod) / atr[i] if atr[i] != 0 else 0
    
    # Calculate DX and ADX
    dx = np.zeros_like(close)
    dx[:] = np.nan
    
    for i in range(timeperiod - 1, len(close)):
        if plus_di[i] + minus_di[i] != 0:
            dx[i] = 100 * abs(plus_di[i] - minus_di[i]) / (plus_di[i] + minus_di[i])
        else:
            dx[i] = 0
    
    # ADX is smoothed DX
    adx = np.zeros_like(close)
    adx[:] = np.nan
    
    # Initial ADX
    adx[2 * timeperiod - 2] = np.mean(dx[timeperiod - 1:2 * timeperiod - 1])
    
    # Subsequent ADX values
    for i in range(2 * timeperiod - 1, len(close)):
        adx[i] = (adx[i - 1] * (timeperiod - 1) + dx[i]) / timeperiod
        
    return adx


def CMO(close: np.ndarray, timeperiod: int = 14) -> np.ndarray:
    """
    Calculate Chande Momentum Oscillator
    
    Args:
        close: Array of closing prices
        timeperiod: Period for CMO calculation
        
    Returns:
        Array of CMO values
    """
    cmo = np.zeros_like(close)
    cmo[:] = np.nan
    
    # Calculate price changes
    deltas = np.diff(close)
    
    for i in range(timeperiod, len(close)):
        period_deltas = deltas[i - timeperiod:i]
        
        sum_up = np.sum(period_deltas[period_deltas > 0])
        sum_down = -np.sum(period_deltas[period_deltas < 0])
        
        if sum_up + sum_down != 0:
            cmo[i] = 100 * (sum_up - sum_down) / (sum_up + sum_down)
        else:
            cmo[i] = 0
            
    return cmo


def CCI(high: np.ndarray, low: np.ndarray, close: np.ndarray, timeperiod: int = 14) -> np.ndarray:
    """
    Calculate Commodity Channel Index
    
    Args:
        high: Array of high prices
        low: Array of low prices
        close: Array of closing prices
        timeperiod: Period for CCI calculation
        
    Returns:
        Array of CCI values
    """
    cci = np.zeros_like(close)
    cci[:] = np.nan
    
    # Calculate typical price
    tp = (high + low + close) / 3
    
    for i in range(timeperiod - 1, len(close)):
        # Moving average of typical price
        ma = np.mean(tp[i - timeperiod + 1:i + 1])
        
        # Mean deviation
        md = np.mean(np.abs(tp[i - timeperiod + 1:i + 1] - ma))
        
        if md != 0:
            cci[i] = (tp[i] - ma) / (0.015 * md)
        else:
            cci[i] = 0
            
    return cci


def MFI(high: np.ndarray, low: np.ndarray, close: np.ndarray, volume: np.ndarray, 
        timeperiod: int = 14) -> np.ndarray:
    """
    Calculate Money Flow Index
    
    Args:
        high: Array of high prices
        low: Array of low prices
        close: Array of closing prices
        volume: Array of volume data
        timeperiod: Period for MFI calculation
        
    Returns:
        Array of MFI values
    """
    mfi = np.zeros_like(close)
    mfi[:] = np.nan
    
    # Calculate typical price and money flow
    tp = (high + low + close) / 3
    mf = tp * volume
    
    for i in range(timeperiod, len(close)):
        # Separate positive and negative money flow
        positive_mf = 0
        negative_mf = 0
        
        for j in range(i - timeperiod + 1, i + 1):
            if j > 0:
                if tp[j] > tp[j - 1]:
                    positive_mf += mf[j]
                elif tp[j] < tp[j - 1]:
                    negative_mf += mf[j]
        
        if negative_mf != 0:
            mfr = positive_mf / negative_mf
            mfi[i] = 100 - (100 / (1 + mfr))
        else:
            mfi[i] = 100
            
    return mfi


def OBV(close: np.ndarray, volume: np.ndarray) -> np.ndarray:
    """
    Calculate On Balance Volume
    
    Args:
        close: Array of closing prices
        volume: Array of volume data
        
    Returns:
        Array of OBV values
    """
    obv = np.zeros_like(close)
    obv[0] = volume[0]
    
    for i in range(1, len(close)):
        if close[i] > close[i - 1]:
            obv[i] = obv[i - 1] + volume[i]
        elif close[i] < close[i - 1]:
            obv[i] = obv[i - 1] - volume[i]
        else:
            obv[i] = obv[i - 1]
            
    return obv


def SAR(high: np.ndarray, low: np.ndarray, acceleration: float = 0.02, 
        maximum: float = 0.2) -> np.ndarray:
    """
    Calculate Parabolic SAR
    
    Args:
        high: Array of high prices
        low: Array of low prices
        acceleration: Acceleration factor (default: 0.02)
        maximum: Maximum acceleration (default: 0.2)
        
    Returns:
        Array of SAR values
    """
    sar = np.zeros_like(high)
    ep = 0  # Extreme point
    af = acceleration  # Acceleration factor
    uptrend = True
    
    # Initialize
    sar[0] = low[0]
    ep = high[0]
    
    for i in range(1, len(high)):
        if uptrend:
            sar[i] = sar[i - 1] + af * (ep - sar[i - 1])
            
            if low[i] <= sar[i]:
                uptrend = False
                sar[i] = ep
                ep = low[i]
                af = acceleration
            else:
                if high[i] > ep:
                    ep = high[i]
                    af = min(af + acceleration, maximum)
                    
                # Make sure SAR is below the low
                sar[i] = min(sar[i], low[i], low[i - 1])
        else:
            sar[i] = sar[i - 1] - af * (sar[i - 1] - ep)
            
            if high[i] >= sar[i]:
                uptrend = True
                sar[i] = ep
                ep = high[i]
                af = acceleration
            else:
                if low[i] < ep:
                    ep = low[i]
                    af = min(af + acceleration, maximum)
                    
                # Make sure SAR is above the high
                sar[i] = max(sar[i], high[i], high[i - 1])
                
    return sar


def MOM(close: np.ndarray, timeperiod: int = 10) -> np.ndarray:
    """
    Calculate Momentum
    
    Args:
        close: Array of closing prices
        timeperiod: Period for momentum calculation
        
    Returns:
        Array of momentum values
    """
    mom = np.zeros_like(close)
    mom[:] = np.nan
    
    for i in range(timeperiod, len(close)):
        mom[i] = close[i] - close[i - timeperiod]
        
    return mom


def ROC(close: np.ndarray, timeperiod: int = 10) -> np.ndarray:
    """
    Calculate Rate of Change
    
    Args:
        close: Array of closing prices
        timeperiod: Period for ROC calculation
        
    Returns:
        Array of ROC values (in percentage)
    """
    roc = np.zeros_like(close)
    roc[:] = np.nan
    
    for i in range(timeperiod, len(close)):
        if close[i - timeperiod] != 0:
            roc[i] = ((close[i] - close[i - timeperiod]) / close[i - timeperiod]) * 100
        else:
            roc[i] = 0
            
    return roc


def STOCHRSI(close: np.ndarray, timeperiod: int = 14, fastk_period: int = 5, 
             fastd_period: int = 3, fastd_matype: int = 0) -> Tuple[np.ndarray, np.ndarray]:
    """
    Calculate Stochastic RSI
    
    Args:
        close: Array of closing prices
        timeperiod: Period for RSI calculation
        fastk_period: Period for %K calculation
        fastd_period: Period for %D calculation
        fastd_matype: MA type for %D calculation (default: 0=SMA)
        
    Returns:
        Tuple of (fastk, fastd)
    """
    # First calculate RSI
    rsi = RSI(close, timeperiod)
    
    # Then calculate Stochastic on RSI
    fastk = np.zeros_like(rsi)
    fastk[:] = np.nan
    
    for i in range(fastk_period - 1, len(rsi)):
        if not np.isnan(rsi[i - fastk_period + 1:i + 1]).any():
            period_rsi = rsi[i - fastk_period + 1:i + 1]
            max_rsi = np.max(period_rsi)
            min_rsi = np.min(period_rsi)
            
            if max_rsi != min_rsi:
                fastk[i] = 100 * (rsi[i] - min_rsi) / (max_rsi - min_rsi)
            else:
                fastk[i] = 50
    
    # Calculate %D (SMA of %K)
    fastd = SMA(fastk, timeperiod=fastd_period)
    
    return fastk, fastd


def WILLR(high: np.ndarray, low: np.ndarray, close: np.ndarray, timeperiod: int = 14) -> np.ndarray:
    """
    Calculate Williams %R
    
    Args:
        high: Array of high prices
        low: Array of low prices
        close: Array of closing prices
        timeperiod: Period for Williams %R calculation
        
    Returns:
        Array of Williams %R values
    """
    willr = np.zeros_like(close)
    willr[:] = np.nan
    
    for i in range(timeperiod - 1, len(close)):
        highest = np.max(high[i - timeperiod + 1:i + 1])
        lowest = np.min(low[i - timeperiod + 1:i + 1])
        
        if highest != lowest:
            willr[i] = -100 * (highest - close[i]) / (highest - lowest)
        else:
            willr[i] = -50
            
    return willr


def ULTOSC(high: np.ndarray, low: np.ndarray, close: np.ndarray,
           timeperiod1: int = 7, timeperiod2: int = 14, timeperiod3: int = 28) -> np.ndarray:
    """
    Calculate Ultimate Oscillator
    
    Args:
        high: Array of high prices
        low: Array of low prices
        close: Array of closing prices
        timeperiod1: First period (default: 7)
        timeperiod2: Second period (default: 14)
        timeperiod3: Third period (default: 28)
        
    Returns:
        Array of Ultimate Oscillator values
    """
    ultosc = np.zeros_like(close)
    ultosc[:] = np.nan
    
    # Calculate True Low and Buying Pressure
    true_low = np.zeros_like(close)
    buying_pressure = np.zeros_like(close)
    
    true_low[0] = low[0]
    buying_pressure[0] = close[0] - true_low[0]
    
    for i in range(1, len(close)):
        true_low[i] = min(low[i], close[i - 1])
        buying_pressure[i] = close[i] - true_low[i]
    
    # Calculate True Range
    tr = np.zeros_like(close)
    tr[0] = high[0] - low[0]
    
    for i in range(1, len(close)):
        hl = high[i] - low[i]
        hc = abs(high[i] - close[i - 1])
        lc = abs(low[i] - close[i - 1])
        tr[i] = max(hl, hc, lc)
    
    # Calculate Ultimate Oscillator
    max_period = max(timeperiod1, timeperiod2, timeperiod3)
    
    for i in range(max_period - 1, len(close)):
        bp_sum1 = np.sum(buying_pressure[i - timeperiod1 + 1:i + 1])
        bp_sum2 = np.sum(buying_pressure[i - timeperiod2 + 1:i + 1])
        bp_sum3 = np.sum(buying_pressure[i - timeperiod3 + 1:i + 1])
        
        tr_sum1 = np.sum(tr[i - timeperiod1 + 1:i + 1])
        tr_sum2 = np.sum(tr[i - timeperiod2 + 1:i + 1])
        tr_sum3 = np.sum(tr[i - timeperiod3 + 1:i + 1])
        
        avg1 = bp_sum1 / tr_sum1 if tr_sum1 != 0 else 0
        avg2 = bp_sum2 / tr_sum2 if tr_sum2 != 0 else 0
        avg3 = bp_sum3 / tr_sum3 if tr_sum3 != 0 else 0
        
        ultosc[i] = 100 * (4 * avg1 + 2 * avg2 + avg3) / 7
        
    return ultosc