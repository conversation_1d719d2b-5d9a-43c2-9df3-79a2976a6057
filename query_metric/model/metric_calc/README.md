# 指标计算模块使用文档

## 模块说明

本模块提供加密货币技术指标计算功能，支持30+种技术指标，包括基础行情指标和高级技术分析指标。模块通过 KuCoin API 获取实时和历史K线数据，使用纯 Python/NumPy 实现的高性能指标计算。

## 依赖安装

```bash
# Python 依赖
pip install -r requirements.txt
```

## 模块导入与初始化

```python
from query_metric.model.metric_calc.metric_calculator import MetricCalculator

# 创建计算器实例（建议作为单例使用）
calculator = MetricCalculator()

# 可选：重新加载配置
calculator.reload_model()
```

## 核心方法详解

### 核心方法：calculate()

#### 入参说明

| 参数 | 类型 | 必填 | 说明 | 示例 |
|------|------|------|------|------|
| symbol | string | 是 | 交易对或合约代码 | "BTC-USDT" (现货)<br>"BTCUSDTM" (期货) |
| indicator | string | 是 | 指标名称（大小写不敏感） | "RSI", "MACD", "BBANDS" |
| interval | string | 否* | K线时间间隔<br>*技术指标必填 | "1min", "1hour", "1day" |
| market_type | string | 否 | 市场类型，默认"spot" | "spot", "futures" |
| time_range | dict | 否 | 时间范围参数 | 见下方详细说明 |
| params | dict | 否 | 指标特定参数 | {"period": 14} |

**time_range 参数结构**：
```python
{
    "start_time": 1704067200,  # 开始时间戳（秒）
    "end_time": 1704672000,    # 结束时间戳（秒）
    "limit": 100               # 限制返回数据条数
}
```

#### 出参说明

**成功响应结构**：

基础指标响应示例（如 PRICE, CHANGE_RATE_24H）：
```python
{
    "success": True,
    "indicator": "CHANGE_RATE_24H",
    "symbol": "BTC-USDT",
    "market_type": "spot",
    "timestamp": 1704067200,
    "data": {
        "latest_value": 2.34,
        "description": "24小时涨跌幅"
    }
}
```

技术指标响应示例（如 RSI, MACD, BBANDS）：
```python
{
    "success": True,
    "indicator": "RSI",
    "symbol": "BTC-USDT",
    "interval": "1hour",            # 技术指标特有
    "market_type": "spot",
    "timestamp": 1704067200,
    "data": {
        # 通用字段
        "period": 14,               # 指标参数
        "latest_value": 52.34,      # 最新计算值
        "values": [45.23, 48.56, ...],     # 时间序列数据
        "timestamps": [1704067200, ...],   # 对应时间戳
        
        # MACD 特有字段：
        "parameters": {
            "fast_period": 12,
            "slow_period": 26,
            "signal_period": 9
        },
        "macd_line": [...],         # MACD线
        "signal_line": [...],       # 信号线  
        "histogram": [...],         # 柱状图
        "latest_values": {          # 最新值集合
            "macd": 1.23,
            "signal": 0.89,
            "histogram": 0.34
        },
        
        # 布林带特有字段：
        "upper_band": [...],        # 上轨
        "middle_band": [...],       # 中轨
        "lower_band": [...],        # 下轨
        "latest_values": {
            "upper": 50000,
            "middle": 48000,
            "lower": 46000
        }
    }
}
```

**失败响应结构**：
```python
{
    "success": False,
    "error": "数据不足，无法计算指标",
    "indicator": "RSI",
    "symbol": "BTC-USDT"
}
```

### 1. calculate() - 单个指标计算

```python
result = calculator.calculate(
    symbol="BTC-USDT",      # 必填：交易对
    indicator="RSI",        # 必填：指标名称
    interval="1hour",       # 条件必填：技术指标需要
    market_type="spot",     # 可选：默认"spot"
    time_range={            # 可选：时间范围
        "start_time": 1704067200,
        "end_time": 1704672000,
        "limit": 100
    },
    params={"period": 14}   # 可选：指标参数
)
```

### 2. calculate_batch() - 批量指标计算

```python
results = calculator.calculate_batch(
    symbol="ETH-USDT",
    indicators=["RSI", "MACD", "KDJ", "BBANDS"],
    interval="4hour",
    market_type="spot",
    time_range={
        "start_time": 1704067200,
        "end_time": 1704672000
    },
    params={
        "RSI": {"period": 21},
        "MACD": {"fast_period": 10}
    }
)
```

## 使用示例

### 示例1：计算基础行情指标

```python
# 获取现货最新价格
result = calculator.calculate(
    symbol="BTC-USDT",
    indicator="PRICE"
)
print(f"BTC 最新价: ${result['data']['latest_value']}")

# 获取24小时涨跌幅
result = calculator.calculate(
    symbol="ETH-USDT", 
    indicator="CHANGE_RATE_24H"
)
print(f"ETH 24小时涨跌: {result['data']['latest_value']}%")
```

### 示例2：计算技术指标

```python
# 计算 RSI
result = calculator.calculate(
    symbol="BTC-USDT",
    indicator="RSI",
    interval="1hour",
    params={"period": 14}
)

if result['success']:
    data = result['data']
    print(f"RSI 当前值: {data['latest_value']}")
    print(f"历史数据点: {len(data['values'])}")

# 计算 MACD
result = calculator.calculate(
    symbol="BTC-USDT",
    indicator="MACD",
    interval="4hour"
)

if result['success']:
    data = result['data']
    print(f"MACD线: {data['macd_line'][-1]}")
    print(f"信号线: {data['signal_line'][-1]}")
    print(f"柱状图: {data['histogram'][-1]}")
```

### 示例3：获取历史时间序列数据

```python
import time
from datetime import datetime, timedelta

# 获取最近7天的 RSI 数据
end_time = int(time.time())
start_time = end_time - 7 * 24 * 3600

result = calculator.calculate(
    symbol="BTC-USDT",
    indicator="RSI", 
    interval="1hour",
    time_range={
        "start_time": start_time,
        "end_time": end_time
    }
)

if result['success']:
    data = result['data']
    timestamps = data['timestamps']
    values = data['values']
    
    # 打印每天的 RSI 值
    for i in range(0, len(timestamps), 24):  # 每24小时取一个点
        dt = datetime.fromtimestamp(timestamps[i])
        print(f"{dt.strftime('%Y-%m-%d')}: RSI = {values[i]:.2f}")
```

### 示例4：批量计算多个指标

```python
# 同时计算多个指标
results = calculator.calculate_batch(
    symbol="ETH-USDT",
    indicators=["RSI", "MACD", "ATR", "BBANDS"],
    interval="1hour"
)

for indicator, result in results.items():
    if result['success']:
        latest = result['data']['latest_value']
        print(f"{indicator}: {latest}")
    else:
        print(f"{indicator}: 计算失败 - {result['error']}")
```

### 示例5：期货市场指标

```python
# 获取期货标记价格
result = calculator.calculate(
    symbol="BTCUSDTM",  # 期货合约代码
    indicator="MARK_PRICE",
    market_type="futures"
)
print(f"BTC永续合约标记价格: ${result['data']['latest_value']}")

# 计算期货技术指标
result = calculator.calculate(
    symbol="ETHUSDTM",
    indicator="RSI",
    interval="4hour",
    market_type="futures",
    params={"period": 21}
)
```

## 支持的指标列表

### 基础指标（不需要 interval）

#### 现货基础指标
| 指标名称 | 说明 | 返回字段 |
|----------|------|----------|
| PRICE / LAST_PRICE | 最新成交价 | latest_value |
| HIGH_24H | 24小时最高价 | latest_value |
| LOW_24H | 24小时最低价 | latest_value |
| VOLUME_24H | 24小时成交量 | latest_value |
| TURNOVER_24H | 24小时成交额 | latest_value |
| CHANGE_RATE_24H | 24小时涨跌幅(%) | latest_value |
| CHANGE_PRICE_24H | 24小时涨跌额 | latest_value |
| AVG_PRICE_24H | 24小时均价 | latest_value |

#### 期货基础指标
| 指标名称 | 说明 | 返回字段 |
|----------|------|----------|
| MARK_PRICE | 标记价格 | latest_value |
| INDEX_PRICE | 指数价格 | latest_value |
| 其他 | 与现货相同 | - |

### 技术指标（需要 interval）

#### 趋势指标
| 指标 | 说明 | 默认参数 | 可选参数 | 特殊返回字段 |
|------|------|----------|----------|--------------|
| SMA | 简单移动平均线 | period=20 | period(5-200) | values, timestamps |
| EMA | 指数移动平均线 | period=20 | period(5-200) | values, timestamps |
| WMA | 加权移动平均线 | period=20 | period(5-200) | values, timestamps |
| DEMA | 双重指数移动平均 | period=20 | period(5-200) | values, timestamps |
| TEMA | 三重指数移动平均 | period=20 | period(5-200) | values, timestamps |
| MACD | 指数平滑异同移动平均线 | fast=12, slow=26, signal=9 | fast_period, slow_period, signal_period | macd_line, signal_line, histogram |
| VWMA | 成交量加权移动平均 | period=20 | period(5-200) | values, timestamps |

#### 动量指标
| 指标 | 说明 | 默认参数 | 可选参数 | 特殊返回字段 |
|------|------|----------|----------|--------------|
| RSI | 相对强弱指数 | period=14 | period(2-100) | values |
| KDJ | 随机指标 | k=9, d=3, j=3 | k_period, d_period, j_period | latest_values{k,d,j} |
| STOCH | 随机振荡器 | k=14, d=3 | k_period, d_period | k_values, d_values |
| STOCH_RSI | 随机RSI | period=14, k=3, d=3 | period, k_period, d_period | k_values, d_values |
| WILLR | 威廉姆斯%R | period=14 | period(5-100) | values |
| CCI | 商品通道指数 | period=20 | period(5-100) | values |
| CMO | 钱德动量振荡器 | period=14 | period(5-100) | values |
| MOMENTUM | 动量指标 | period=10 | period(1-100) | values |
| ROC | 变动率 | period=10 | period(1-100) | values |
| ULTOSC | 终极振荡器 | p1=7, p2=14, p3=28 | period1, period2, period3 | values |

#### 波动率指标
| 指标 | 说明 | 默认参数 | 可选参数 | 特殊返回字段 |
|------|------|----------|----------|--------------|
| BBANDS | 布林带 | period=20, std=2 | period, std_dev | upper_band, middle_band, lower_band |
| BB_WIDTH | 布林带宽度 | period=20, std=2 | period, std_dev | values |
| BB_PERCENT_B | 布林带%B | period=20, std=2 | period, std_dev | values |
| ATR | 平均真实范围 | period=14 | period(1-100) | values |
| KELTNER | 肯特纳通道 | period=20, mult=2 | period, multiplier | upper_band, middle_band, lower_band |

#### 成交量指标
| 指标 | 说明 | 默认参数 | 可选参数 | 特殊返回字段 |
|------|------|----------|----------|--------------|
| VWAP | 成交量加权平均价格 | - | - | values |
| OBV | 平衡成交量 | - | - | values |
| AD | 累积/派发线 | - | - | values |
| MFI | 资金流量指数 | period=14 | period(2-100) | values |
| CMF | 蔡金资金流 | period=20 | period(5-100) | values |
| CHAIKIN_OSC | 蔡金振荡器 | fast=3, slow=10 | fast_period, slow_period | values |
| VOL_OSC | 成交量振荡器 | fast=10, slow=30 | fast_period, slow_period | values |

#### 其他指标
| 指标 | 说明 | 默认参数 | 可选参数 | 特殊返回字段 |
|------|------|----------|----------|--------------|
| ADX | 平均趋向指数 | period=14 | period(5-100) | values |
| AROON | 阿隆指标 | period=14 | period(5-100) | aroon_up, aroon_down |
| AO | 动量振荡器 | - | - | values |
| SAR | 抛物线SAR | accel=0.02, max=0.2 | acceleration, maximum | values |
| VORTEX | 涡流指标 | period=14 | period(2-100) | vi_plus, vi_minus |

## 时间间隔参数

支持的 interval 值：
- `1min`, `3min`, `5min`, `15min`, `30min`
- `1hour`, `2hour`, `4hour`, `6hour`, `8hour`, `12hour`
- `1day`, `1week`

## 数据获取说明

### K线数据来源
- **现货数据**：通过 KuCoin Spot API 获取
- **期货数据**：通过 KuCoin Futures API 获取
- **数据更新**：实时获取，无缓存机制
- **历史数据**：最多可获取1500条K线数据

### 交易对格式
- **现货**：使用 "-" 分隔，如 "BTC-USDT", "ETH-USDT"
- **期货**：永续合约格式，如 "BTCUSDTM", "ETHUSDTM"

### 时间范围使用
```python
# 方式1：使用时间戳
time_range = {
    "start_time": 1704067200,  # 2024-01-01 00:00:00
    "end_time": 1704672000,    # 2024-01-07 00:00:00
}

# 方式2：使用limit限制数量
time_range = {
    "limit": 100  # 获取最近100条K线
}

# 方式3：组合使用
time_range = {
    "start_time": 1704067200,
    "end_time": 1704672000,
    "limit": 500  # 时间范围内最多500条
}
```

## 返回数据格式说明

### 响应结构设计原则
1. **统一结构**：所有指标都遵循相同的顶层结构
2. **无重复字段**：元信息（indicator, symbol, market_type等）只在顶层出现
3. **数据隔离**：所有指标计算结果都封装在 `data` 字段中
4. **类型区分**：
   - 基础指标：返回单一最新值和描述
   - 技术指标：返回时间序列数据和计算参数

### 数据数组对应关系
```python
# timestamps 和 values 一一对应
timestamps = [1704067200, 1704070800, 1704074400, ...]
values = [45.23, 46.78, 44.56, ...]

# 使用示例
for i in range(len(timestamps)):
    dt = datetime.fromtimestamp(timestamps[i])
    value = values[i]
    print(f"{dt}: {value}")
```

### 指标值说明
- 各指标返回原始计算值，不包含信号解释
- 用户可根据指标值自行判断交易信号
- 例如：RSI < 30 可能表示超卖，RSI > 70 可能表示超买

## 注意事项

### 1. 数据量限制
- 单次请求最多返回1500条K线数据
- 建议使用 `time_range` 参数控制数据量
- 较小的时间间隔（如1min）数据量增长快

### 2. 指标计算要求
某些指标需要足够的历史数据：

| 指标 | 最少K线数 | 计算说明 |
|------|----------|----------|
| SMA(20) | 20 | 需要20条数据计算平均值 |
| EMA(20) | 20 | 需要20条数据计算指数平均 |
| RSI(14) | 15 | 需要14+1条数据计算涨跌 |
| MACD | 35 | slow(26) + signal(9) |
| BBANDS(20) | 20 | 需要20条数据计算标准差 |
| ATR(14) | 15 | 需要前一天数据计算真实范围 |

### 3. 错误处理
```python
# 完整的错误处理示例
try:
    result = calculator.calculate(
        symbol="BTC-USDT",
        indicator="RSI",
        interval="1hour"
    )
    
    if result['success']:
        # 处理成功结果
        data = result['data']
        print(f"计算成功: {data['latest_value']}")
    else:
        # 处理计算失败
        error = result.get('error', '未知错误')
        print(f"计算失败: {error}")
        
except Exception as e:
    # 处理异常情况
    print(f"发生异常: {str(e)}")
```

### 4. 批量计算返回格式
```python
# calculate_batch 返回字典格式
results = {
    "RSI": {
        "success": True,
        "indicator": "RSI",
        "data": {...}
    },
    "MACD": {
        "success": False,
        "error": "数据不足"
    }
}
```