# -*- coding:utf-8 -*-
# @Time    : 2025/7/14
# <AUTHOR> York
# @Description: 指标计算模块
# @Jira: 
# @Wiki:

"""
指标计算模块

该模块提供了完整的加密货币技术指标计算功能，支持现货和期货市场。

主要组件：
- MetricCalculator: 统一的指标计算入口类（推荐使用）
- DataFetcher: 数据获取类
- TechnicalIndicators: 技术指标计算类
- MetricConfig: 配置管理类

使用示例：
    >>> from query_metric.model.metric_calc import MetricCalculator
    >>> 
    >>> # 创建计算器实例
    >>> calculator = MetricCalculator()
    >>> 
    >>> # 计算单个指标
    >>> result = calculator.calculate(
    ...     symbol="BTC-USDT",
    ...     indicator="RSI",
    ...     interval="1hour"
    ... )
    >>> 
    >>> # 批量计算
    >>> results = calculator.calculate_batch(
    ...     symbol="ETH-USDT",
    ...     indicators=["RSI", "MACD", "KDJ"],
    ...     interval="4hour"
    ... )
"""

from .metric_calculator import MetricCalculator
from .data_fetcher import DataFetcher
from .indicators import TechnicalIndicators
from .config_helper import MetricConfig
from . import custom_indicators

__all__ = [
    'MetricCalculator',    # 推荐使用的主入口
    'DataFetcher',         # 数据获取
    'TechnicalIndicators', # 技术指标计算
    'MetricConfig',        # 配置管理
    'custom_indicators'    # 自定义指标实现
]