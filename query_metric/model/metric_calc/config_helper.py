"""
配置辅助类 - 从主配置系统获取指标计算相关配置

该模块提供了与原 MetricConfig 类兼容的接口，但从主配置系统获取配置值。
"""

from typing import Dict, Any, Optional
from query_metric.config import CFG


class MetricConfigHelper:
    """指标计算服务配置辅助类，从主配置系统获取配置"""
    
    @classmethod
    def _get_config_value(cls, key: str, default: Any = None) -> Any:
        """从主配置系统获取配置值"""
        value = getattr(CFG, key, default)
        # 如果值为 None，返回默认值
        return value if value is not None else default
    
    # KuCoin API 端点
    @property
    def SPOT_API_BASE(self) -> str:
        return self._get_config_value('spot_api_base', 'https://api.kucoin.com')
    
    @property
    def FUTURES_API_BASE(self) -> str:
        return self._get_config_value('futures_api_base', 'https://api-futures.kucoin.com')
    
    # API 路径
    @property
    def SPOT_CANDLES_PATH(self) -> str:
        return self._get_config_value('spot_candles_path', '/api/v1/market/candles')
    
    @property
    def SPOT_STATS_PATH(self) -> str:
        return self._get_config_value('spot_stats_path', '/api/v1/market/stats')
    
    @property
    def FUTURES_CANDLES_PATH(self) -> str:
        return self._get_config_value('futures_candles_path', '/api/v1/kline/query')
    
    @property
    def FUTURES_CONTRACT_PATH(self) -> str:
        return self._get_config_value('futures_contract_path', '/api/v1/contracts/{symbol}')
    
    @property
    def FUTURES_STATS_PATH(self) -> str:
        return self._get_config_value('futures_stats_path', '/_api_kumex/web-front/contracts/{symbol}')
    
    # 请求配置
    @property
    def REQUEST_TIMEOUT(self) -> int:
        return self._get_config_value('request_timeout', 30)
    
    @property
    def MAX_RETRIES(self) -> int:
        return self._get_config_value('max_retries', 3)
    
    @property
    def RETRY_DELAY(self) -> int:
        return self._get_config_value('retry_delay', 1)
    
    @property
    def VERIFY_SSL(self) -> bool:
        return self._get_config_value('verify_ssl', True)
    
    # K线数据配置
    @property
    def KLINE_INTERVALS(self) -> Dict[str, str]:
        return self._get_config_value('kline_intervals', {
            "1min": "1min",
            "3min": "3min", 
            "5min": "5min",
            "15min": "15min",
            "30min": "30min",
            "1hour": "1hour",
            "2hour": "2hour",
            "4hour": "4hour",
            "6hour": "6hour",
            "8hour": "8hour",
            "12hour": "12hour",
            "1day": "1day",
            "1week": "1week"
        })
    
    # 期货K线颗粒度映射
    @property
    def FUTURES_GRANULARITY(self) -> Dict[str, int]:
        return self._get_config_value('futures_granularity', {
            "1min": 1,
            "3min": 3,
            "5min": 5,
            "15min": 15,
            "30min": 30,
            "1hour": 60,
            "2hour": 120,
            "4hour": 240,
            "6hour": 360,
            "8hour": 480,
            "12hour": 720,
            "1day": 1440,
            "1week": 10080
        })
    
    # 指标计算默认参数
    @property
    def DEFAULT_PARAMS(self) -> Dict[str, Dict[str, Any]]:
        return self._get_config_value('default_indicator_params', {
            "RSI": {"period": 14},
            "MACD": {"fast_period": 12, "slow_period": 26, "signal_period": 9},
            "BBANDS": {"period": 20, "std_dev": 2},
            "SMA": {"period": 20},
            "EMA": {"period": 20},
            "KDJ": {"fast_k_period": 9, "slow_k_period": 3, "slow_d_period": 3},
            "ATR": {"period": 14},
            "ADX": {"period": 14},
            "CCI": {"period": 20},
            "MOM": {"period": 10},
            "ROC": {"period": 10},
            "WILLR": {"period": 14},
            "STOCH": {"fast_k_period": 14, "slow_k_period": 3, "slow_d_period": 3},
            "VWAP": {"period": None},
        })
    
    # 数据限制
    @property
    def MAX_KLINE_LIMIT(self) -> int:
        return self._get_config_value('max_kline_limit', 1500)
    
    @property
    def MIN_KLINE_REQUIRED(self) -> Dict[str, int]:
        return self._get_config_value('min_kline_required', {
            "RSI": 15,
            "MACD": 35,
            "BBANDS": 20,
            "SMA": 20,
            "EMA": 20,
            "KDJ": 10,
            "ATR": 15,
            "ADX": 30,
            "CCI": 20,
            "MOM": 11,
            "ROC": 11,
            "WILLR": 15,
            "STOCH": 15,
            "VWAP": 1,
        })
    
    @classmethod
    def get_indicator_params(cls, indicator: str) -> Dict[str, Any]:
        """
        获取指标的默认参数
        
        Args:
            indicator: 指标名称
            
        Returns:
            指标的默认参数字典
        """
        helper = cls()
        return helper.DEFAULT_PARAMS.get(indicator.upper(), {})
    
    @classmethod
    def get_min_klines_required(cls, indicator: str) -> int:
        """
        获取计算指标所需的最小K线数量
        
        Args:
            indicator: 指标名称
            
        Returns:
            最小K线数量
        """
        helper = cls()
        return helper.MIN_KLINE_REQUIRED.get(indicator.upper(), 30)


# 创建一个实例以兼容原有的使用方式
MetricConfig = MetricConfigHelper()