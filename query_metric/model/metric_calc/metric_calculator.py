"""
统一的指标计算入口类

该模块是整个指标计算系统的主入口，提供了统一的接口来计算各种技术指标。
"""

import time
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from query_metric.common.log_util import logger
from .config_helper import MetricConfig
from .data_fetcher import DataFetcher
from .indicators import TechnicalIndicators


class MetricCalculator:
    """
    指标计算器主类 - MCP 注册的核心接口
    
    Attributes:
        data_fetcher: 数据获取器
        tech_indicators: 技术指标计算器
        config: 配置对象
        
    Example:
        >>> # 创建计算器实例
        >>> calculator = MetricCalculator()
        >>> 
        >>> # 计算单个指标
        >>> result = calculator.calculate(
        ...     symbol="BTC-USDT",
        ...     indicator="RSI",
        ...     interval="1hour"
        ... )
        >>> 
        >>> # 批量计算多个指标
        >>> result = calculator.calculate_batch(
        ...     symbol="ETH-USDT",
        ...     indicators=["RSI", "MACD", "KDJ"],
        ...     interval="4hour"
        ... )
    """
    
    def __init__(self):
        """初始化指标计算器"""
        self.data_fetcher = DataFetcher()
        self.tech_indicators = TechnicalIndicators()
        self.config = MetricConfig
        logger.info("MetricCalculator 初始化成功")
        
    def reload_model(self) -> bool:
        """
        重新加载模型配置
        
        该方法可用于热更新配置，无需重启服务。
        
        Returns:
            是否成功重新加载
            
        Example:
            >>> success = calculator.reload_model()
            >>> if success:
            ...     print("配置重新加载成功")
        """
        try:
            # 这里可以添加重新加载配置的逻辑
            # 例如：重新读取配置文件、更新参数等
            logger.info("MetricCalculator 配置重新加载成功")
            return True
        except Exception as e:
            logger.error(f"MetricCalculator 配置重新加载失败: {str(e)}")
            return False
    
    def calculate(self, symbol: str, indicator: str, 
                 interval: Optional[str] = None,
                 market_type: str = "spot",
                 time_range: Optional[Dict[str, Any]] = None,
                 params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        计算单个指标
        
        这是最核心的方法，支持计算所有类型的指标。
        
        Args:
            symbol: 交易对（如 "BTC-USDT"）或合约代码（如 "BTCUSDTM"）
            indicator: 指标名称，支持的指标：
                - 基础指标：PRICE, HIGH_24H, LOW_24H, VOLUME_24H, CHANGE_RATE_24H
                - 技术指标：RSI, MACD, BBANDS, KDJ, ATR, VWAP, SMA, EMA, 等
            interval: K线时间间隔（技术指标必需），如 "1min", "1hour", "1day"
            market_type: 市场类型，"spot"（现货）或 "futures"（期货）
            time_range: 时间范围（可选），格式：
                {
                    "start_time": 1234567890,  # 开始时间戳
                    "end_time": 1234567890,    # 结束时间戳
                    "limit": 100               # 数据条数限制
                }
            params: 指标特定参数（可选），如：
                - RSI: {"period": 14}
                - MACD: {"fast_period": 12, "slow_period": 26, "signal_period": 9}
                - BBANDS: {"period": 20, "std_dev": 2}
                
        Returns:
            计算结果字典，格式根据指标类型而定：
            {
                "success": True,
                "indicator": "RSI",
                "symbol": "BTC-USDT",
                "market_type": "spot",
                "data": {...},  # 具体的指标数据
                "timestamp": 1234567890,
                "calculation_time": 25.34
            }
            
        Raises:
            ValueError: 参数错误或不支持的指标
            Exception: 计算过程中的其他错误
            
        Example:
            >>> # 计算最新价格（基础指标）
            >>> result = calculator.calculate("BTC-USDT", "PRICE")
            >>> print(f"BTC 最新价: ${result['data']['value']}")
            >>> 
            >>> # 计算 RSI（技术指标）
            >>> result = calculator.calculate(
            ...     symbol="ETH-USDT",
            ...     indicator="RSI",
            ...     interval="1hour",
            ...     params={"period": 21}
            ... )
            >>> 
            >>> # 计算指定时间范围的 MACD
            >>> result = calculator.calculate(
            ...     symbol="BTC-USDT",
            ...     indicator="MACD",
            ...     interval="4hour",
            ...     time_range={
            ...         "start_time": 1234567890,
            ...         "end_time": 1234567890,
            ...         "limit": 200
            ...     }
            ... )
        """
        start_time = time.time()
        
        try:
            indicator_upper = indicator.upper()
            
            # 基础指标列表
            basic_indicators = [
                "PRICE", "LAST_PRICE", "HIGH_24H", "LOW_24H", 
                "VOLUME_24H", "TURNOVER_24H", "CHANGE_RATE_24H", 
                "CHANGE_PRICE_24H", "AVG_PRICE_24H",
                "MARK_PRICE", "INDEX_PRICE"  # 期货特有
            ]
            
            # 技术指标列表 - 涵盖CSV文件中的所有指标
            technical_indicators = [
                # 基础指标已实现
                "RSI", "MACD", "BBANDS", "KDJ", "ATR", "VWAP",
                "SMA", "EMA", "WMA", "DEMA", "TEMA",
                # 新增指标
                "AD", "AROON", "ADX", "AO", "BB_PERCENT_B", "BB_WIDTH",
                "CMF", "CHAIKIN_OSC", "CMO", "CCI", "DPO", "DM",
                "DONCHIAN", "ELDER_FI", "EMA_CROSS", "ENVELOPES",
                "HMA", "ICHIMOKU", "KELTNER", "MA_CROSS", "MFI",
                "MOMENTUM", "OBV", "SAR", "PIVOT", "PRICE_CHANNEL",
                "PRICE_OSC", "PVT", "ROC", "STOCH", "STOCH_RSI",
                "SMOOTHED_MA", "WILLR", "ULTOSC", "VWMA", "VOL_OSC",
                "VORTEX", "WILLIAMS_ALLIGATOR", "WILLIAMS_FRACTAL",
                "ACCELERATOR_OSC", "GUPPY_MMA", "MA_DOUBLE", "MA_MULTIPLE",
                "MA_TRIPLE", "SUPERTREND", "TSI", "VOLUME_PROFILE_FIXED",
                "VOLUME_PROFILE_VISIBLE"
            ]
            
            # 准备统一的返回结构
            result = {
                "success": True,
                "indicator": indicator_upper,
                "symbol": symbol,
                "market_type": market_type,
                "timestamp": int(time.time())
            }
            
            # 处理基础指标
            if indicator_upper in basic_indicators:
                data = self._calculate_basic_indicator(
                    symbol, indicator_upper, market_type
                )
                result["data"] = data
                
            # 处理技术指标
            elif indicator_upper in technical_indicators:
                if not interval:
                    raise ValueError(f"技术指标 {indicator} 需要提供 interval 参数")
                
                # 解析时间范围参数
                start_ts = time_range.get("start_time") if time_range else None
                end_ts = time_range.get("end_time") if time_range else None
                limit = time_range.get("limit") if time_range else None
                
                # 解析指标参数
                indicator_params = params or {}
                
                # 调用对应的计算方法
                if indicator_upper == "RSI":
                    period = indicator_params.get("period", 14)
                    data = self.tech_indicators.calculate_rsi(
                        symbol, interval, period, market_type,
                        limit, start_ts, end_ts
                    )
                    
                elif indicator_upper == "MACD":
                    fast = indicator_params.get("fast_period", 12)
                    slow = indicator_params.get("slow_period", 26)
                    signal = indicator_params.get("signal_period", 9)
                    data = self.tech_indicators.calculate_macd(
                        symbol, interval, fast, slow, signal,
                        market_type, limit, start_ts, end_ts
                    )
                    
                elif indicator_upper == "BBANDS":
                    period = indicator_params.get("period", 20)
                    std_dev = indicator_params.get("std_dev", 2.0)
                    data = self.tech_indicators.calculate_bollinger_bands(
                        symbol, interval, period, std_dev,
                        market_type, limit, start_ts, end_ts
                    )
                    
                elif indicator_upper == "KDJ":
                    fast_k = indicator_params.get("fast_k_period", 9)
                    slow_k = indicator_params.get("slow_k_period", 3)
                    slow_d = indicator_params.get("slow_d_period", 3)
                    data = self.tech_indicators.calculate_kdj(
                        symbol, interval, fast_k, slow_k, slow_d,
                        market_type, limit, start_ts, end_ts
                    )
                    
                elif indicator_upper == "ATR":
                    period = indicator_params.get("period", 14)
                    data = self.tech_indicators.calculate_atr(
                        symbol, interval, period, market_type,
                        limit, start_ts, end_ts
                    )
                    
                elif indicator_upper == "VWAP":
                    data = self.tech_indicators.calculate_vwap(
                        symbol, interval, market_type,
                        limit, start_ts, end_ts
                    )
                    
                elif indicator_upper in ["SMA", "EMA", "WMA", "DEMA", "TEMA"]:
                    periods = indicator_params.get("periods", [20])
                    if isinstance(periods, int):
                        periods = [periods]
                    data = self.tech_indicators.calculate_moving_averages(
                        symbol, interval, indicator_upper, periods,
                        market_type, limit, start_ts, end_ts
                    )
                
                # 新增指标的路由处理
                elif indicator_upper == "AD":
                    data = self.tech_indicators.calculate_accumulation_distribution(
                        symbol, interval, market_type, limit, start_ts, end_ts
                    )
                    
                elif indicator_upper == "AROON":
                    period = indicator_params.get("period", 14)
                    data = self.tech_indicators.calculate_aroon(
                        symbol, interval, period, market_type, limit, start_ts, end_ts
                    )
                    
                elif indicator_upper == "ADX":
                    period = indicator_params.get("period", 14)
                    data = self.tech_indicators.calculate_adx(
                        symbol, interval, period, market_type, limit, start_ts, end_ts
                    )
                    
                elif indicator_upper == "AO":
                    fast_period = indicator_params.get("fast_period", 5)
                    slow_period = indicator_params.get("slow_period", 34)
                    data = self.tech_indicators.calculate_awesome_oscillator(
                        symbol, interval, fast_period, slow_period,
                        market_type, limit, start_ts, end_ts
                    )
                    
                elif indicator_upper == "BB_PERCENT_B":
                    period = indicator_params.get("period", 20)
                    std_dev = indicator_params.get("std_dev", 2.0)
                    data = self.tech_indicators.calculate_bollinger_percent_b(
                        symbol, interval, period, std_dev,
                        market_type, limit, start_ts, end_ts
                    )
                    
                elif indicator_upper == "BB_WIDTH":
                    period = indicator_params.get("period", 20)
                    std_dev = indicator_params.get("std_dev", 2.0)
                    data = self.tech_indicators.calculate_bollinger_width(
                        symbol, interval, period, std_dev,
                        market_type, limit, start_ts, end_ts
                    )
                    
                elif indicator_upper == "CMF":
                    period = indicator_params.get("period", 20)
                    data = self.tech_indicators.calculate_chaikin_money_flow(
                        symbol, interval, period, market_type, limit, start_ts, end_ts
                    )
                    
                elif indicator_upper == "CHAIKIN_OSC":
                    fast_period = indicator_params.get("fast_period", 3)
                    slow_period = indicator_params.get("slow_period", 10)
                    data = self.tech_indicators.calculate_chaikin_oscillator(
                        symbol, interval, fast_period, slow_period,
                        market_type, limit, start_ts, end_ts
                    )
                    
                elif indicator_upper == "CMO":
                    period = indicator_params.get("period", 14)
                    data = self.tech_indicators.calculate_chande_momentum_oscillator(
                        symbol, interval, period, market_type, limit, start_ts, end_ts
                    )
                    
                elif indicator_upper == "CCI":
                    period = indicator_params.get("period", 14)
                    data = self.tech_indicators.calculate_cci(
                        symbol, interval, period, market_type, limit, start_ts, end_ts
                    )
                    
                elif indicator_upper == "MFI":
                    period = indicator_params.get("period", 14)
                    data = self.tech_indicators.calculate_money_flow_index(
                        symbol, interval, period, market_type, limit, start_ts, end_ts
                    )
                    
                elif indicator_upper == "OBV":
                    data = self.tech_indicators.calculate_obv(
                        symbol, interval, market_type, limit, start_ts, end_ts
                    )
                    
                elif indicator_upper == "SAR":
                    acceleration = indicator_params.get("acceleration", 0.02)
                    maximum = indicator_params.get("maximum", 0.2)
                    data = self.tech_indicators.calculate_parabolic_sar(
                        symbol, interval, acceleration, maximum,
                        market_type, limit, start_ts, end_ts
                    )
                    
                elif indicator_upper == "MOMENTUM":
                    period = indicator_params.get("period", 10)
                    data = self.tech_indicators.calculate_momentum(
                        symbol, interval, period, market_type, limit, start_ts, end_ts
                    )
                    
                elif indicator_upper == "ROC":
                    period = indicator_params.get("period", 10)
                    data = self.tech_indicators.calculate_roc(
                        symbol, interval, period, market_type, limit, start_ts, end_ts
                    )
                    
                elif indicator_upper == "STOCH":
                    fastk_period = indicator_params.get("fastk_period", 5)
                    slowk_period = indicator_params.get("slowk_period", 3)
                    slowd_period = indicator_params.get("slowd_period", 3)
                    data = self.tech_indicators.calculate_stochastic(
                        symbol, interval, fastk_period, slowk_period, slowd_period,
                        market_type, limit, start_ts, end_ts
                    )
                    
                elif indicator_upper == "STOCH_RSI":
                    timeperiod = indicator_params.get("timeperiod", 14)
                    fastk_period = indicator_params.get("fastk_period", 5)
                    fastd_period = indicator_params.get("fastd_period", 3)
                    data = self.tech_indicators.calculate_stochastic_rsi(
                        symbol, interval, timeperiod, fastk_period, fastd_period,
                        market_type, limit, start_ts, end_ts
                    )
                    
                elif indicator_upper == "WILLR":
                    period = indicator_params.get("period", 14)
                    data = self.tech_indicators.calculate_williams_r(
                        symbol, interval, period, market_type, limit, start_ts, end_ts
                    )
                    
                elif indicator_upper == "ULTOSC":
                    period1 = indicator_params.get("period1", 7)
                    period2 = indicator_params.get("period2", 14)
                    period3 = indicator_params.get("period3", 28)
                    data = self.tech_indicators.calculate_ultimate_oscillator(
                        symbol, interval, period1, period2, period3,
                        market_type, limit, start_ts, end_ts
                    )
                    
                # TEMA 已在 moving_averages 中处理，无需重复
                    
                elif indicator_upper == "VWMA":
                    period = indicator_params.get("period", 20)
                    data = self.tech_indicators.calculate_vwma(
                        symbol, interval, period, market_type, limit, start_ts, end_ts
                    )
                    
                elif indicator_upper == "VOL_OSC":
                    fast_period = indicator_params.get("fast_period", 5)
                    slow_period = indicator_params.get("slow_period", 10)
                    data = self.tech_indicators.calculate_volume_oscillator(
                        symbol, interval, fast_period, slow_period,
                        market_type, limit, start_ts, end_ts
                    )
                    
                elif indicator_upper == "VORTEX":
                    period = indicator_params.get("period", 14)
                    data = self.tech_indicators.calculate_vortex_indicator(
                        symbol, interval, period, market_type, limit, start_ts, end_ts
                    )
                    
                else:
                    # 对于尚未实现的指标，提供友好的错误信息
                    supported_indicators = [
                        "RSI", "MACD", "BBANDS", "KDJ", "ATR", "VWAP",
                        "SMA", "EMA", "WMA", "DEMA", "TEMA", "AD", "AROON", 
                        "ADX", "AO", "BB_PERCENT_B", "BB_WIDTH", "CMF", 
                        "CHAIKIN_OSC", "CMO", "CCI", "MFI", "OBV", "SAR",
                        "MOMENTUM", "ROC", "STOCH", "STOCH_RSI", "WILLR",
                        "ULTOSC", "VWMA", "VOL_OSC", "VORTEX"
                    ]
                    if indicator_upper in supported_indicators:
                        raise ValueError(f"技术指标 {indicator} 已支持但路由配置有误")
                    else:
                        remaining_indicators = [
                            "DPO", "DM", "DONCHIAN", "ELDER_FI", "EMA_CROSS", 
                            "ENVELOPES", "HMA", "ICHIMOKU", "KELTNER", "MA_CROSS",
                            "PIVOT", "PRICE_CHANNEL", "PRICE_OSC", "PVT", 
                            "SMOOTHED_MA", "WILLIAMS_ALLIGATOR", "WILLIAMS_FRACTAL",
                            "ACCELERATOR_OSC", "GUPPY_MMA", "MA_DOUBLE", "MA_MULTIPLE",
                            "MA_TRIPLE", "SUPERTREND", "TSI", "VOLUME_PROFILE_FIXED",
                            "VOLUME_PROFILE_VISIBLE"
                        ]
                        if indicator_upper in remaining_indicators:
                            raise ValueError(f"技术指标 {indicator} 计划支持，暂未实现")
                        else:
                            raise ValueError(f"不支持的技术指标: {indicator}")
                
                result["interval"] = interval
                result["data"] = data
                
            else:
                raise ValueError(f"不支持的指标: {indicator}")
            
            # 计算总耗时
            elapsed_time = (time.time() - start_time) * 1000
            
            logger.info(f"指标计算成功 {symbol} {indicator}, 耗时: {elapsed_time:.2f}ms")
            return result
            
        except Exception as e:
            elapsed_time = (time.time() - start_time) * 1000
            logger.error(f"指标计算失败 {symbol} {indicator}: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def calculate_batch(self, symbol: str, indicators: List[str],
                       interval: Optional[str] = None,
                       market_type: str = "spot",
                       time_range: Optional[Dict[str, Any]] = None,
                       params: Optional[Dict[str, Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        批量计算多个指标
        
        一次性计算多个指标，提高效率。特别适合需要同时获取多个指标的场景。
        
        Args:
            symbol: 交易对或合约代码
            indicators: 指标名称列表，如 ["RSI", "MACD", "KDJ"]
            interval: K线时间间隔（技术指标必需）
            market_type: 市场类型
            time_range: 时间范围
            params: 各指标的参数字典，如：
                {
                    "RSI": {"period": 14},
                    "MACD": {"fast_period": 12, "slow_period": 26}
                }
                
        Returns:
            批量计算结果：
            {
                "success": True,
                "symbol": "BTC-USDT",
                "market_type": "spot",
                "results": {
                    "RSI": {...},      # 各指标的计算结果
                    "MACD": {...},
                    "KDJ": {...}
                },
                "summary": {           # 汇总信息
                    "total": 3,
                    "successful": 3,
                    "failed": 0,
                    "overall_signal": "bullish"
                },
                "errors": [],          # 错误信息
                "timestamp": 1234567890,
                "total_calculation_time": 156.78
            }
            
        Example:
            >>> # 批量计算常用技术指标
            >>> result = calculator.calculate_batch(
            ...     symbol="BTC-USDT",
            ...     indicators=["RSI", "MACD", "BBANDS", "KDJ", "VWAP"],
            ...     interval="1hour"
            ... )
            >>> 
            >>> # 查看汇总信号
            >>> print(f"综合信号: {result['summary']['overall_signal']}")
            >>> 
            >>> # 使用自定义参数
            >>> result = calculator.calculate_batch(
            ...     symbol="ETH-USDT",
            ...     indicators=["RSI", "MACD"],
            ...     interval="4hour",
            ...     params={
            ...         "RSI": {"period": 21},
            ...         "MACD": {"fast_period": 10, "slow_period": 20}
            ...     }
            ... )
        """
        start_time = time.time()
        
        results = {
            "success": True,
            "symbol": symbol,
            "market_type": market_type,
            "interval": interval,
            "results": {},
            "summary": {
                "total": len(indicators),
                "successful": 0,
                "failed": 0
            },
            "errors": [],
            "timestamp": int(time.time())
        }
        
        # 分离基础指标和技术指标
        basic_indicators = []
        tech_indicators = []
        
        for indicator in indicators:
            if indicator.upper() in ["PRICE", "LAST_PRICE", "HIGH_24H", "LOW_24H", 
                                    "VOLUME_24H", "TURNOVER_24H", "CHANGE_RATE_24H",
                                    "MARK_PRICE", "INDEX_PRICE"]:
                basic_indicators.append(indicator)
            else:
                tech_indicators.append(indicator)
        
        # 批量计算基础指标
        if basic_indicators:
            for indicator in basic_indicators:
                try:
                    result = self.calculate(
                        symbol, indicator, None, market_type,
                        time_range, params.get(indicator) if params else None
                    )
                    if result.get("success", True):
                        # Remove internal fields before adding to results
                        indicator_result = {k: v for k, v in result.items() 
                                         if k not in ["success", "timestamp"]}
                        results["results"][indicator.upper()] = indicator_result
                        results["summary"]["successful"] += 1
                    else:
                        results["errors"].append(f"{indicator}: {result.get('error', 'Unknown error')}")
                        results["summary"]["failed"] += 1
                except Exception as e:
                    results["errors"].append(f"{indicator}: {str(e)}")
                    results["summary"]["failed"] += 1
        
        # 批量计算技术指标（可以优化为一次获取K线数据）
        if tech_indicators:
            if not interval:
                results["success"] = False
                results["errors"].append("技术指标需要提供 interval 参数")
                return results
            
            # 使用 TechnicalIndicators 的批量计算方法
            try:
                batch_result = self.tech_indicators.calculate_all_indicators(
                    symbol, interval, market_type, tech_indicators,
                    time_range.get("limit") if time_range else None
                )
                
                for indicator in tech_indicators:
                    indicator_upper = indicator.upper()
                    if indicator_upper in batch_result["indicators"]:
                        results["results"][indicator_upper] = batch_result["indicators"][indicator_upper]
                        results["summary"]["successful"] += 1
                    else:
                        if indicator_upper in [e.split(":")[0] for e in batch_result.get("errors", [])]:
                            error_msg = next((e for e in batch_result["errors"] if e.startswith(indicator_upper)), "计算失败")
                            results["errors"].append(error_msg)
                        else:
                            results["errors"].append(f"{indicator}: 未返回结果")
                        results["summary"]["failed"] += 1
                
                # 添加综合信号
                if "overall_signal" in batch_result:
                    results["summary"]["overall_signal"] = batch_result["overall_signal"]
                    
            except Exception as e:
                logger.error(f"批量计算技术指标失败: {str(e)}")
                for indicator in tech_indicators:
                    results["errors"].append(f"{indicator}: {str(e)}")
                    results["summary"]["failed"] += 1
        
        # 计算总耗时
        elapsed_time = (time.time() - start_time) * 1000
        
        # 设置成功状态
        results["success"] = results["summary"]["failed"] == 0
        
        logger.info(f"批量指标计算完成 {symbol}, "
                   f"成功: {results['summary']['successful']}, "
                   f"失败: {results['summary']['failed']}, "
                   f"耗时: {elapsed_time:.2f}ms")
        
        return results
    
    def _calculate_basic_indicator(self, symbol: str, indicator: str,
                                 market_type: str) -> Dict[str, Any]:
        """
        计算基础指标（内部方法）
        
        Args:
            symbol: 交易对或合约代码
            indicator: 指标名称
            market_type: 市场类型
            
        Returns:
            指标数据字典
        """
        if market_type == "spot":
            data = self.data_fetcher.get_spot_basic_data(symbol)
            
            indicator_mapping = {
                "PRICE": ("last", "最新成交价"),
                "LAST_PRICE": ("last", "最新成交价"),
                "HIGH_24H": ("high", "24小时最高价"),
                "LOW_24H": ("low", "24小时最低价"),
                "VOLUME_24H": ("vol", "24小时成交量"),
                "TURNOVER_24H": ("volValue", "24小时成交额"),
                "CHANGE_RATE_24H": ("changeRate", "24小时涨跌幅"),
                "CHANGE_PRICE_24H": ("changePrice", "24小时涨跌额"),
                "AVG_PRICE_24H": ("averagePrice", "24小时均价")
            }
            
        elif market_type == "futures":
            data = self.data_fetcher.get_futures_basic_data(symbol)
            
            indicator_mapping = {
                "PRICE": ("lastTradePrice", "最新成交价"),
                "LAST_PRICE": ("lastTradePrice", "最新成交价"),
                "MARK_PRICE": ("markPrice", "标记价格"),
                "INDEX_PRICE": ("indexPrice", "指数价格"),
                "HIGH_24H": ("highPrice", "24小时最高价"),
                "LOW_24H": ("lowPrice", "24小时最低价"),
                "VOLUME_24H": ("volume24h", "24小时成交量"),
                "TURNOVER_24H": ("turnover24h", "24小时成交额"),
                "CHANGE_RATE_24H": ("priceChgPct", "24小时涨跌幅")
            }
        else:
            raise ValueError(f"不支持的市场类型: {market_type}")
        
        if indicator not in indicator_mapping:
            raise ValueError(f"不支持的基础指标: {indicator}")
        
        field_name, description = indicator_mapping[indicator]
        value = data.get(field_name)
        
        # 转换数据类型
        if value is not None:
            try:
                # 涨跌幅需要转换为百分比
                if indicator == "CHANGE_RATE_24H":
                    value = float(value) * 100
                else:
                    value = float(value)
            except:
                pass
        
        return {
            "latest_value": value,
            "description": description
        }
    
    def get_supported_indicators(self, market_type: str = "all") -> Dict[str, List[str]]:
        """
        获取支持的指标列表
        
        Args:
            market_type: 市场类型，"spot"、"futures" 或 "all"
            
        Returns:
            支持的指标分类列表
            
        Example:
            >>> indicators = calculator.get_supported_indicators()
            >>> print("基础指标:", indicators["basic"])
            >>> print("技术指标:", indicators["technical"])
        """
        spot_basic = [
            "PRICE", "HIGH_24H", "LOW_24H", "VOLUME_24H",
            "TURNOVER_24H", "CHANGE_RATE_24H", "CHANGE_PRICE_24H",
            "AVG_PRICE_24H"
        ]
        
        futures_basic = [
            "PRICE", "MARK_PRICE", "INDEX_PRICE", "HIGH_24H",
            "LOW_24H", "VOLUME_24H", "TURNOVER_24H", "CHANGE_RATE_24H"
        ]
        
        technical = [
            # 基础指标
            "RSI", "MACD", "BBANDS", "KDJ", "ATR", "VWAP",
            "SMA", "EMA", "WMA", "DEMA", "TEMA",
            # 新增技术指标 
            "AD", "AROON", "ADX", "AO", "BB_PERCENT_B", "BB_WIDTH",
            "CMF", "CHAIKIN_OSC", "CMO", "CCI", "MFI", "OBV", 
            "SAR", "MOMENTUM", "ROC", "STOCH", "STOCH_RSI",
            "WILLR", "ULTOSC", "VWMA", "VOL_OSC", "VORTEX",
            # 计划支持的指标
            "DPO", "DM", "DONCHIAN", "ELDER_FI", "EMA_CROSS", 
            "ENVELOPES", "HMA", "ICHIMOKU", "KELTNER", "MA_CROSS",
            "PIVOT", "PRICE_CHANNEL", "PRICE_OSC", "PVT", 
            "SMOOTHED_MA", "WILLIAMS_ALLIGATOR", "WILLIAMS_FRACTAL",
            "ACCELERATOR_OSC", "GUPPY_MMA", "MA_DOUBLE", "MA_MULTIPLE",
            "MA_TRIPLE", "SUPERTREND", "TSI", "VOLUME_PROFILE_FIXED",
            "VOLUME_PROFILE_VISIBLE"
        ]
        
        if market_type == "spot":
            return {
                "basic": spot_basic,
                "technical": technical
            }
        elif market_type == "futures":
            return {
                "basic": futures_basic,
                "technical": technical
            }
        else:  # all
            return {
                "basic": {
                    "spot": spot_basic,
                    "futures": futures_basic
                },
                "technical": technical
            }
    
    def get_indicator_info(self, indicator: str) -> Dict[str, Any]:
        """
        获取指标的详细信息
        
        Args:
            indicator: 指标名称
            
        Returns:
            指标的详细信息，包括描述、参数说明等
            
        Example:
            >>> info = calculator.get_indicator_info("RSI")
            >>> print(info["description"])
            >>> print("默认参数:", info["default_params"])
        """
        indicator_info = {
            "RSI": {
                "name": "相对强弱指数",
                "description": "衡量价格变动的速度和幅度，识别超买超卖状态",
                "type": "momentum",
                "default_params": {"period": 14},
                "value_range": "0-100",
                "signals": {
                    ">70": "超买",
                    "<30": "超卖"
                }
            },
            "MACD": {
                "name": "指数平滑异同移动平均线",
                "description": "通过短期和长期EMA的差异，识别趋势变化和买卖信号",
                "type": "trend",
                "default_params": {
                    "fast_period": 12,
                    "slow_period": 26,
                    "signal_period": 9
                },
                "signals": {
                    "金叉": "MACD线上穿信号线",
                    "死叉": "MACD线下穿信号线"
                }
            },
            "BBANDS": {
                "name": "布林带",
                "description": "利用价格标准差绘制上下轨道，识别价格波动范围",
                "type": "volatility",
                "default_params": {
                    "period": 20,
                    "std_dev": 2
                },
                "signals": {
                    "突破上轨": "可能回调",
                    "突破下轨": "可能反弹"
                }
            },
            "KDJ": {
                "name": "随机指标",
                "description": "由K、D、J三线组成，判断超买超卖和交叉信号",
                "type": "momentum",
                "default_params": {
                    "fast_k_period": 9,
                    "slow_k_period": 3,
                    "slow_d_period": 3
                },
                "value_range": "0-100",
                "signals": {
                    "金叉": "K线上穿D线",
                    "死叉": "K线下穿D线",
                    ">80": "超买",
                    "<20": "超卖"
                }
            },
            # 可以继续添加更多指标信息
        }
        
        indicator_upper = indicator.upper()
        if indicator_upper in indicator_info:
            return indicator_info[indicator_upper]
        else:
            return {
                "name": indicator_upper,
                "description": "暂无详细描述",
                "type": "unknown",
                "default_params": self.config.get_indicator_params(indicator_upper)
            }