"""
数据获取类 - 负责从 KuCoin API 获取基础和K线数据

该模块提供了统一的接口来获取现货和期货的市场数据，包括基础指标和K线数据。
"""

import time
import requests
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from query_metric.common.log_util import logger
from .config_helper import MetricConfig


class DataFetcher:
    """
    数据获取类，负责从 KuCoin API 获取市场数据
    
    该类提供了获取现货和期货市场数据的统一接口，包括：
    - 基础市场数据（价格、成交量、涨跌幅等）
    - K线历史数据
    - 合约信息
    
    Attributes:
        config (MetricConfig): 配置对象
        session (requests.Session): HTTP 会话对象
    
    Example:
        >>> fetcher = DataFetcher()
        >>> # 获取现货基础数据
        >>> spot_data = fetcher.get_spot_basic_data("BTC-USDT")
        >>> print(f"BTC 最新价: {spot_data['last']}")
        >>> 
        >>> # 获取K线数据
        >>> klines = fetcher.get_spot_klines("BTC-USDT", "1hour", limit=100)
        >>> print(f"获取了 {len(klines)} 条K线数据")
    """
    
    def __init__(self):
        """初始化数据获取器"""
        self.config = MetricConfig
        self.session = requests.Session()
        self.session.headers.update({
            "Content-Type": "application/json",
            "User-Agent": "MetricCalculator/1.0"
        })
        # 设置SSL验证选项
        self.session.verify = self.config.VERIFY_SSL
        if not self.config.VERIFY_SSL:
            logger.warning("SSL证书验证已关闭，这可能存在安全风险")
    
    def _make_request(self, url: str, params: Optional[Dict] = None, 
                     retries: int = None) -> Union[Dict[str, Any], List[Any]]:
        """
        发送 HTTP 请求（无重试机制）
        
        Args:
            url: 请求 URL
            params: 查询参数
            retries: 重试次数（已废弃，保留参数以兼容）
            
        Returns:
            API 响应数据
            
        Raises:
            requests.RequestException: 请求失败
            ValueError: API 返回错误
        """
        try:
            # 打印完整的请求URL
            full_url = url
            if params:
                from urllib.parse import urlencode
                full_url = f"{url}?{urlencode(params)}"
            logger.info(f"发起HTTP请求 - {full_url}")
            
            # 发送请求，超时设置为3秒
            response = self.session.get(
                url, 
                params=params, 
                timeout=3,  # 固定3秒超时
                verify=self.config.VERIFY_SSL
            )
            response.raise_for_status()
            
            # Check if response is JSON
            content_type = response.headers.get('content-type', '')
            if 'application/json' not in content_type:
                raise ValueError(f"Non-JSON response: {content_type}")
            
            data = response.json()
            # 新API返回格式处理
            if isinstance(data, dict) and "code" in data:
                if data["code"] == 200 or data["code"] == "200":
                    return data.get("data", [])
                else:
                    raise ValueError(f"API 错误: {data.get('msg', 'Unknown error')}")
            else:
                # 兼容旧格式或直接返回数据的情况
                return data
                
        except requests.Timeout:
            logger.error(f"请求超时 (3秒): {url}")
            raise
        except Exception as e:
            logger.error(f"请求失败: {str(e)}")
            raise
    
    def get_spot_basic_data(self, symbol: str) -> Dict[str, Any]:
        """
        获取现货基础市场数据
        
        包括：最新价、24小时最高/最低价、成交量、成交额、涨跌幅等
        
        Args:
            symbol: 交易对，如 "BTC-USDT"
            
        Returns:
            包含市场数据的字典：
            {
                "symbol": "BTC-USDT",
                "last": "45000.5",  # 最新成交价
                "high": "46000",    # 24H最高价
                "low": "44000",     # 24H最低价
                "vol": "1234.56",   # 24H成交量
                "volValue": "56789012.34",  # 24H成交额
                "changePrice": "500.5",     # 24H涨跌额
                "changeRate": "0.0112",     # 24H涨跌幅
                "averagePrice": "45123.4"   # 24H均价
            }
            
        Example:
            >>> data = fetcher.get_spot_basic_data("ETH-USDT")
            >>> print(f"ETH 24小时涨跌幅: {float(data['changeRate']) * 100:.2f}%")
        """
        url = f"{self.config.SPOT_API_BASE}{self.config.SPOT_STATS_PATH}"
        # 新API使用symbols参数
        params = {"symbols": symbol}
        
        start_time = time.time()
        try:
            data = self._make_request(url, params)
            elapsed = (time.time() - start_time) * 1000
            
            # 新API返回的是列表，需要提取第一个元素
            if isinstance(data, list) and len(data) > 0:
                tick_data = data[0]
                # 转换数据格式以兼容旧的字段名
                result = {
                    "symbol": tick_data.get("symbol"),
                    "last": tick_data.get("lastTradedPrice"),
                    "high": tick_data.get("marketChange24h", {}).get("high", tick_data.get("high")),
                    "low": tick_data.get("marketChange24h", {}).get("low", tick_data.get("low")),
                    "vol": tick_data.get("marketChange24h", {}).get("vol", tick_data.get("vol")),
                    "volValue": tick_data.get("marketChange24h", {}).get("volValue", tick_data.get("volValue")),
                    "changePrice": tick_data.get("marketChange24h", {}).get("changePrice", tick_data.get("changePrice")),
                    "changeRate": tick_data.get("marketChange24h", {}).get("changeRate", tick_data.get("changeRate")),
                    "averagePrice": tick_data.get("averagePrice")
                }
                logger.info(f"获取现货基础数据成功 {symbol}, 耗时: {elapsed:.2f}ms")
                return result
            else:
                raise ValueError(f"无效的响应数据: {data}")
                
        except Exception as e:
            logger.error(f"获取现货基础数据失败 {symbol}: {str(e)}")
            raise
    
    def get_spot_klines(self, symbol: str, interval: str, 
                       start_time: Optional[int] = None,
                       end_time: Optional[int] = None,
                       limit: Optional[int] = None) -> List[List[Union[str, float]]]:
        """
        获取现货K线数据
        
        Args:
            symbol: 交易对，如 "BTC-USDT"
            interval: 时间间隔，如 "1min", "1hour", "1day"
            start_time: 开始时间戳（秒），可选
            end_time: 结束时间戳（秒），可选  
            limit: 返回数据条数，最大1500，可选
            
        Returns:
            K线数据列表，每条数据格式：
            [
                "1545904980",  # 开盘时间（秒时间戳）
                "3.45",        # 开盘价
                "3.50",        # 最高价
                "3.40",        # 最低价
                "3.48",        # 收盘价
                "456.78",      # 成交量
                "1589.34"      # 成交额
            ]
            
        Example:
            >>> # 获取最近100条1小时K线
            >>> klines = fetcher.get_spot_klines("BTC-USDT", "1hour", limit=100)
            >>> 
            >>> # 获取指定时间范围的K线
            >>> start = int((datetime.now() - timedelta(days=7)).timestamp())
            >>> end = int(datetime.now().timestamp())
            >>> klines = fetcher.get_spot_klines("BTC-USDT", "1day", start, end)
        """
        url = f"{self.config.SPOT_API_BASE}{self.config.SPOT_CANDLES_PATH}"
        
        # 验证时间间隔
        if interval not in self.config.KLINE_INTERVALS:
            raise ValueError(f"不支持的时间间隔: {interval}")
        
        params = {
            "symbol": symbol,
            "type": self.config.KLINE_INTERVALS[interval]
        }
        
        # 添加可选参数 - 新API使用begin和end参数
        if start_time:
            params["begin"] = str(start_time)
        if end_time:
            params["end"] = str(end_time)
        else:
            # 默认获取到当前时间
            params["end"] = str(int(time.time()))
        
        # 打印K线请求详情
        logger.info(f"K线请求详情 - URL: {url}")
        logger.info(f"K线请求参数 - symbol: {symbol}, interval: {interval}, "
                   f"type: {params.get('type')}, begin: {params.get('begin', 'None')}, "
                   f"end: {params.get('end', 'None')}, limit: {limit}")
        
        start_req_time = time.time()
        try:
            data = self._make_request(url, params)
            
            # 处理数据
            klines = data if isinstance(data, list) else []
            logger.info(f"API返回原始数据量: {len(klines)} 条")
            
            # 新API返回的K线数据是倒序的（最新的在前），需要反转
            klines = list(reversed(klines))
            logger.info(f"数据反转后数量: {len(klines)} 条")
            
            # 应用限制 - 获取最新的limit条数据
            if limit and len(klines) > limit:
                logger.info(f"应用limit限制: {len(klines)} -> {limit}")
                klines = klines[-limit:]
                
            elapsed = (time.time() - start_req_time) * 1000
            logger.info(f"获取现货K线数据成功 {symbol} {interval}, "
                       f"最终数量: {len(klines)}, 耗时: {elapsed:.2f}ms")
            
            return klines
            
        except Exception as e:
            logger.error(f"获取现货K线数据失败 {symbol} {interval}: {str(e)}")
            raise
    
    def get_futures_basic_data(self, symbol: str) -> Dict[str, Any]:
        """
        获取期货合约基础数据
        
        包括：最新价、标记价格、指数价格、24小时数据等
        
        Args:
            symbol: 合约代码，如 "BTCUSDTM"
            
        Returns:
            包含合约数据的字典：
            {
                "symbol": "BTCUSDTM",
                "lastTradePrice": "45000.5",    # 最新成交价
                "markPrice": "45010.2",         # 标记价格
                "indexPrice": "45005.8",        # 指数价格
                "highPrice": "46000",           # 24H最高价
                "lowPrice": "44000",            # 24H最低价
                "volume24h": "12345678",        # 24H成交量
                "turnover24h": "567890123.45",  # 24H成交额
                "priceChgPct": "0.0112"         # 24H涨跌幅
            }
            
        Example:
            >>> data = fetcher.get_futures_basic_data("BTCUSDTM")
            >>> print(f"BTC永续合约标记价格: {data['markPrice']}")
        """
        # 处理特殊映射：BTCUSDTM -> XBTUSDTM
        api_symbol = symbol
        if symbol == "BTCUSDTM":
            api_symbol = "XBTUSDTM"
            
        # 使用合约代码直接构建URL
        url = f"{self.config.FUTURES_API_BASE}{self.config.FUTURES_STATS_PATH.format(symbol=api_symbol)}"
        
        start_time = time.time()
        try:
            data = self._make_request(url)
            elapsed = (time.time() - start_time) * 1000
            
            # 新API直接返回合约数据
            if isinstance(data, dict):
                # 转换数据格式以保持兼容性
                result = {
                    "symbol": symbol,
                    "lastTradePrice": str(data.get("lastTradePrice", "")),
                    "markPrice": str(data.get("markPrice", "")),
                    "indexPrice": str(data.get("indexPrice", "")),
                    "highPrice": str(data.get("highPrice", "")),
                    "lowPrice": str(data.get("lowPrice", "")),
                    "volume24h": str(data.get("volumeOf24h", "")),
                    "turnover24h": str(data.get("turnoverOf24h", "")),
                    "priceChgPct": str(data.get("priceChgPct", ""))
                }
                logger.info(f"获取期货基础数据成功 {symbol}, 耗时: {elapsed:.2f}ms")
                return result
            else:
                raise ValueError(f"无效的响应数据格式: {type(data)}")
                
        except Exception as e:
            logger.error(f"获取期货基础数据失败 {symbol}: {str(e)}")
            raise
    
    def get_futures_klines(self, symbol: str, granularity: str,
                          start_time: Optional[int] = None,
                          end_time: Optional[int] = None,
                          limit: Optional[int] = None) -> List[List[Union[str, float]]]:
        """
        获取期货K线数据
        
        Args:
            symbol: 合约代码，如 "BTCUSDTM"
            granularity: 时间颗粒度，如 "1min", "1hour", "1day"
            start_time: 开始时间戳（毫秒），可选
            end_time: 结束时间戳（毫秒），可选
            limit: 返回数据条数，可选
            
        Returns:
            K线数据列表，每条数据格式：
            [
                1545904980000,  # 开盘时间（毫秒时间戳）
                "3.45",         # 开盘价
                "3.50",         # 最高价
                "3.40",         # 最低价
                "3.48",         # 收盘价
                "456.78"        # 成交量
            ]
            
        Example:
            >>> # 获取最近的100条5分钟K线
            >>> klines = fetcher.get_futures_klines("BTCUSDTM", "5min", limit=100)
            >>> 
            >>> # 获取过去24小时的1小时K线
            >>> end = int(datetime.now().timestamp() * 1000)
            >>> start = end - 24 * 3600 * 1000
            >>> klines = fetcher.get_futures_klines("BTCUSDTM", "1hour", start, end)
        """
        url = f"{self.config.FUTURES_API_BASE}{self.config.FUTURES_CANDLES_PATH}"
        
        # 验证时间颗粒度
        if granularity not in self.config.FUTURES_GRANULARITY:
            raise ValueError(f"不支持的时间颗粒度: {granularity}")
        
        # 处理特殊映射：BTCUSDTM -> XBTUSDTM
        api_symbol = symbol
        if symbol == "BTCUSDTM":
            api_symbol = "XBTUSDTM"
        
        params = {
            "symbol": api_symbol,
            "type": granularity  # 新API直接使用字符串格式
        }
        
        # 处理时间参数（新API使用秒时间戳）
        if start_time:
            # 如果传入的是毫秒时间戳，转换为秒
            if start_time > 10000000000:
                start_time = start_time // 1000
            params["begin"] = str(start_time)
            
        if end_time:
            # 如果传入的是毫秒时间戳，转换为秒
            if end_time > 10000000000:
                end_time = end_time // 1000
            params["end"] = str(end_time)
        else:
            # 默认获取到当前时间
            params["end"] = str(int(time.time()))
            
        if not start_time:
            # 如果没指定开始时间，根据limit计算
            if limit and limit > 0:
                # 根据颗粒度计算开始时间
                minutes = self.config.FUTURES_GRANULARITY.get(granularity, 60)
                params["begin"] = str(int(params["end"]) - limit * minutes * 60)
        
        # 打印期货K线请求详情
        logger.info(f"期货K线请求详情 - URL: {url}")
        logger.info(f"期货K线请求参数 - symbol: {api_symbol} (原始: {symbol}), granularity: {granularity}, "
                   f"begin: {params.get('begin', 'None')}, end: {params.get('end', 'None')}, limit: {limit}")
        
        start_req_time = time.time()
        try:
            data = self._make_request(url, params)
            
            # 处理数据
            klines = data if isinstance(data, list) else []
            logger.info(f"期货API返回原始数据量: {len(klines)} 条")
            
            # 新API返回的K线数据是倒序的（最新的在前），需要反转
            klines = list(reversed(klines))
            logger.info(f"期货数据反转后数量: {len(klines)} 条")
            
            # 应用限制 - 获取最新的limit条数据
            if limit and len(klines) > limit:
                logger.info(f"期货应用limit限制: {len(klines)} -> {limit}")
                klines = klines[-limit:]
                
            elapsed = (time.time() - start_req_time) * 1000
            logger.info(f"获取期货K线数据成功 {symbol} {granularity}, "
                       f"最终数量: {len(klines)}, 耗时: {elapsed:.2f}ms")
            
            return klines
            
        except Exception as e:
            logger.error(f"获取期货K线数据失败 {symbol} {granularity}: {str(e)}")
            raise
    
    def prepare_kline_data(self, klines: List[List[Union[str, float]]], 
                          is_futures: bool = False) -> pd.DataFrame:
        """
        将K线数据转换为 DataFrame 格式，方便后续计算
        
        Args:
            klines: K线原始数据
            is_futures: 是否是期货数据（影响时间戳处理）
            
        Returns:
            包含 OHLCV 数据的 DataFrame，列名：
            - timestamp: 时间戳
            - open: 开盘价
            - high: 最高价
            - low: 最低价
            - close: 收盘价
            - volume: 成交量
            
        Example:
            >>> klines = fetcher.get_spot_klines("BTC-USDT", "1hour", limit=100)
            >>> df = fetcher.prepare_kline_data(klines)
            >>> print(df.tail())
        """
        if not klines:
            return pd.DataFrame()
        
        # 转换为 DataFrame
        if is_futures:
            # 期货数据：[timestamp(s), open, close, high, low, turnover, volume]
            # 注意新API中期货数据的顺序变化
            df = pd.DataFrame(klines, columns=['timestamp', 'open', 'close', 'high', 'low', 'turnover', 'volume'])
            # 重新排列列的顺序以匹配标准OHLCV格式
            df = df[['timestamp', 'open', 'high', 'low', 'close', 'volume', 'turnover']]
        else:
            # 现货数据：[timestamp(s), open, close, high, low, volume, turnover]
            # 注意新API中现货数据的顺序也有变化
            df = pd.DataFrame(klines, columns=['timestamp', 'open', 'close', 'high', 'low', 'volume', 'turnover'])
            # 重新排列列的顺序以匹配标准OHLCV格式
            df = df[['timestamp', 'open', 'high', 'low', 'close', 'volume', 'turnover']]
        
        # 转换数据类型
        df['timestamp'] = pd.to_numeric(df['timestamp'])
        for col in ['open', 'high', 'low', 'close', 'volume']:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 设置时间索引
        df['datetime'] = pd.to_datetime(df['timestamp'], unit='s')
        df.set_index('datetime', inplace=True)
        
        # 按时间排序
        df.sort_index(inplace=True)
        
        # 删除任何 NaN 值
        df.dropna(subset=['open', 'high', 'low', 'close'], inplace=True)
        
        return df