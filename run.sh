#!/bin/bash

if [[ "$serverEnv" == "offline" ]];then
  export APOLLO_HOSTS=http://apollo-risk.kucoin:8080
elif [[ "$serverEnv" == "offlineXversion" ]];then
  export APOLLO_HOSTS=http://apollo-risk.kucoin:8080
elif [[ "$serverEnv" == "pre" ]];then
  export APOLLO_HOSTS=http://apollo-risk.kucoin:8080
elif [[ "$serverEnv" == "uat" ]];then
  export APOLLO_HOSTS=http://apollo-risk-uat.kucoin:8080/
else
  export APOLLO_HOSTS=http://apollo.kucoin:8080
  export https_proxy=http://mwg-aws-ns2.kcprd.com:9090
  export no_proxy=localhost,.kcprd.com,kucoin.net,10.0.0.0/8,127.0.0.1
fi
export REQUESTS_CA_BUNDLE=/etc/ssl/certs/ca-certificates.crt

export LOG_PATH=/var/log/kucoin/dc-yousun-risk-device-cls-model
mkdir -p "$LOG_DIR"

exec python main.py runserver