# --*-- conding:utf-8 --*--
# @Time : 2024/12/4
# <AUTHOR> <PERSON>

import time
import traceback

import requests

from query_metric.common.log_util import Logger

logger = Logger(log_name='postStart', log_path='/var/log/kucoin/ai-query-metric')

url = f"http://127.0.0.1:10240/actuator/up"

time.sleep(60)
while True:
    try:
        response = requests.post(url, timeout=30)
        status_code = response.status_code
        if status_code == 200:
            logger.info(f"Executed postStart.py success: {status_code}, Response: {response.text}")
            break

        else:
            logger.error(f"Executed postStart.py failed: {status_code}, Response: {response.text}")

    except requests.HTTPError as http_err:
        logger.error(f"Executed postStart.py error occurred: {http_err.response.status_code}, Body: {http_err.response.text}")
    except requests.RequestException as req_err:
        logger.error(f"Executed postStart.py request execution failed: {traceback.format_exc()}")
    except Exception as e:
        logger.error(f"Executed postStart.py unexpected error occurred: {traceback.format_exc()}")
    finally:
        logger.info("Executed postStart.py retrying in 5 seconds...")
        time.sleep(5)
