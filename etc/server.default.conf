##### 基础配置 #####
##########################################
server_name: ai-query-metric
debug: True
is_apollo: True
secret_key: ''
reload_model: True

##### 服务配置 #####
##########################################
listen_host: 0.0.0.0
listen_port: 10240

##### 时区配置 #####
##########################################
timezone: 'Asia/Shanghai'


##### 数据库相关的配置 #####
##########################################
db_type: 'mongo'
db_host:
db_port:
db_username:
db_password:
db_name: "kc_crawler"


##### Redis 相关的配置 #####
##########################################
redis_url: 127.0.0.1:6379
redis_password:
redis_db: "0"


##### eureka配置 #####
##########################################
eureka_server:
eureka_username: 'admin'
eureka_password:


##### 分批预测 #####
##########################################
use_batch: True
batch_size: 1000000


##### 日志地址 #####
##########################################
log_path: /var/log/kucoin/ai-query-metric


##### 模型文件 #####
##########################################
model_path:
model_name:


MAX_LEN:
vocab_size: