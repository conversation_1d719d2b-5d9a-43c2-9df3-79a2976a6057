# Python工程模版

#### 1. 项目结构

```
├── ci                 # Dockerfile
├── etc                # 配置文件 (.conf, .yaml 等)
├── health.py          # 健康检查脚本
├── main.py            # 入口文件，启动 FastAPI 应用
├── query_metric       # 核心业务逻辑模块
│   ├── __init__.py
│   ├── application.py # FastAPI 应用实例创建
│   ├── base           # 基础组件封装（如 FastAPI、JSON、Error 处理）
│   ├── common         # 通用工具（日志、注册中心、监控等）
│   ├── config.py      # 配置文件、apollo配置导入
│   ├── db_utils       # 数据库连接管理
│   ├── model          # 模型管理
│   │   ├──__init__.py
│   │   ├── xxx_xx     # 算法模型开发目录
│   ├── router.py      # 路由管理
│   ├── server.py      # 服务启动相关逻辑
│   ├── utils          # 其他工具方法
│   ├── setup          # 初始化库包
│   ├── views          # 视图层（API 逻辑）
├── postStart.py       # 容器启动后执行的初始化脚本
├── preStop.py         # 容器停止前执行的清理脚本
├── readme.md          # 项目说明文档
├── requirements.txt   # 依赖包列表
├── run.sh             # 运行脚本
```


#### 2. 软件架构

`Python3.12`, `fast api`


#### 3. 安装教程

1. 安装依赖

Python==3.12本版，请提前安装

```bash
pip install -r requestments.txt

```

#### 4. 使用说明

4.1  配置环境参数

```bash
cp -av etc/server.default etc/server.dev.conf

# 修改一些关键参数
# 比如model 的参数,如下: 
base_url: 
api_key: 
api_version: 
model_name: 
```

4.2 启动服务并测试

```python
python main.py runserver -c etc/server.dev.conf  # 指定本地启动服务的配置文件
"""
/usr/local/Caskroom/miniconda/base/envs/ai-query-metric/bin/python /Users/<USER>/Downloads/ai-query-metric/main.py runserver -c etc/server.dev.conf

 -------------- MS@ai-query-metric V0.0.1
---- **** *---- 
--- * ***  * -- platform.platform()
-- * - ---- --- [config]
- ** ---------- .> apps    :     ai-query-metric
- ** ---------- .> info    :     York@0.0.0.0:10243
- *** --- - --- .> python  :     3.12
-- ******* *--- .> config  :     /Users/<USER>/Downloads/ai-query-metric/etc/server.dev.conf
--- *******---- .> log     :     ./log


INFO:     Started server process [33832]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:10243 (Press CTRL+C to quit)
"""
```
4.3 提交代码

```python
# 本地开发完，请将etc/server.dev.conf配置文件中新增的配置同步到etc/server.default.conf中, dev, sit，生产使用的配置文件为etc/server.default.conf
```


4.3 算法SDK信息

```
仓库地址: 
分支: 
```
