# --*-- conding:utf-8 --*--
# @Time : 2024/12/4
# <AUTHOR> <PERSON>

# import logging
import os
import sys
import socket

import requests

from query_metric.common.log_util import Logger

logger = Logger(log_name='health', log_path='/var/log/kucoin/ai-query-metric')

url = f"http://127.0.0.1:10240/actuator/health"

response = None
try:

    response = requests.get(url, timeout=30)
    status_code = response.status_code
    if status_code == 200:
        logger.info(f"Executed health.py success: {status_code}, Response: {response.text}")

    else:
        logger.error(f"Executed health.py failed: {status_code}, Response: {response.text}")

except requests.HTTPError as http_err:
    logger.error(f"Executed health.py error occurred: {http_err.response.status_code}, Body: {http_err.response.text}")
    sys.exit(2)
except requests.RequestException as req_err:
    logger.error(f"Executed health.py request execution failed: {req_err}")
    sys.exit(1)
except Exception as e:
    logger.error(f"Executed health.py unexpected error occurred: {e}")
    sys.exit(1)
finally:
    logger.info("Executed health.py retrying in 5 seconds...")
