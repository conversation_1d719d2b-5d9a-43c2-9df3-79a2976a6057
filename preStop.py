# --*-- conding:utf-8 --*--
# @Time : 2024/12/4
# <AUTHOR> <PERSON>

import os
import time
import requests

from query_metric.common.log_util import Logger

logger = Logger(log_name='preStop', log_path='/var/log/kucoin/ai-query-metric')


pid_list = os.popen("ps -ef | grep postStart|grep -v grep|awk '{print $2}'").read()
for pid in pid_list.split("\n")[0:-1]:
    logger.info("kill -9 postStart, pid: {}".format(pid))
    os.system("kill -9 %s" % pid)

time.sleep(1)

url = f"http://127.0.0.1:10240/actuator/down"

while True:

    try:
        response = requests.post(url, timeout=3)
        status_code = response.status_code
        if status_code == 200:
            logger.info(f"Executed preStop.py success: {status_code}, Response: {response.text}")
            break
        else:
            logger.warning(f"Executed preStop.py failed: {status_code}, Response: {response.text}")

    except requests.HTTPError as http_err:
        logger.error(f"Executed preStop.py HTTP error occurred: {http_err.response.status_code}, Body: {http_err.response.text}")
    except requests.RequestException as req_err:
        logger.error(f"Executed preStop.py request execution failed: {req_err}")
    except Exception as e:
        logger.error(f"Executed preStop.py unexpected error occurred: {e}")
    finally:
        logger.info("Executed preStop.py retrying in 3 seconds...")
        time.sleep(3)
