# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml
.pdm-python
.pdm-build

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
.idea/

# VSCode
.vscode/
*.code-workspace

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# Vim
[._]*.s[a-v][a-z]
[._]*.sw[a-p]
[._]s[a-rt-v][a-z]
[._]ss[a-gi-z]
[._]sw[a-p]
Session.vim
Sessionx.vim
.netrwhist
tags
[._]*.un~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*
.org-id-locations
*_archive
*_flymake.*
/eshell/history
/eshell/lastdir
/elpa/
*.rel
/auto/
.cask/
dist/
flycheck_*.el
/server/
.projectile
.dir-locals.el
/network-security.data

# Sublime Text
*.tmlanguage.cache
*.tmPreferences.cache
*.stTheme.cache
*.sublime-workspace
*.sublime-project
sftp-config.json
sftp-config-alt*.json
Package Control.last-run
Package Control.ca-list
Package Control.ca-bundle
Package Control.system-ca-bundle
Package Control.cache/
Package Control.ca-certs/
Package Control.merged-ca-bundle
Package Control.user-ca-bundle
oscrypto-ca-bundle.crt
bh_unicode_properties.cache
GitHub.sublime-settings

# Project specific
*.log
*.pid
logs/
temp/
tmp/
cache/
.cache/

# Database files
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup
*.old

# Test files
test_*.py
*_test.py
tests/

# 指标测试脚本 (开发调试用)
test_indicators_interactive.py
test_indicators_simple.py
test_api.py

# Documentation build
docs/_build/
docs/_static/
docs/_templates/

# Virtual environments
.uv/
.virtualenv/

# Claude specific
CLAUDE.md

# Local configuration
local_config.py
.env.local
.env.*.local

# Temporary files
*.tmp
*.temp
*.swp
*.swo

# Archive files
*.zip
*.tar
*.tar.gz
*.tgz
*.rar
*.7z

# Security sensitive files
*.pem
*.key
*.cert
*.crt
*.p12
*.pfx

# IDE specific
.project
.classpath
.settings/
*.iml
*.ipr
*.iws

# Node modules (if any frontend components)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Output directories
output/
out/
bin/

# Compiled files
*.pyc
*.pyo
*.pyd
*.dll
*.exe
*.o
*.a

# Data files
data/
*.csv
*.xlsx
*.xls
*.json
!requirements.json
!package.json

# Model files
models/
*.pkl
*.h5
*.model
*.weights

# Monitoring
.prometheus/
metrics/

# Apollo configuration cache
.apollo/

# Redis dump
dump.rdb

# MongoDB dump
mongodump/

# Kubernetes
*.kubeconfig
kubeconfig

# Docker
.dockerignore
docker-compose.override.yml

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# Ansible
*.retry
ansible.cfg

# Secrets
secrets/
.secrets/
*.secret